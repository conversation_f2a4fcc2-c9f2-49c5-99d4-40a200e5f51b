/**
 * @file Task_App.c
 * <AUTHOR> name void Task_OLED(void *para)
 * @brief 任务实现层
 * @version 0.1
 * @date 2025-07-12
 * 
 * @copyright Copyright (c) 2025
 * 
 */
#include "Task_App.h"

#define INDEX 2.5f //转向调试系数
#define CAMERA_MAX_ERROR_COUNT 5 //摄像头最大错误次数

static uint32_t lost_time = 0;
uint8_t current_path = 0;  // 0:A->C, 1:C->B, 2:B->D, 3:D->A, 4:完成 (全局变量，供OLED显示)


/*Data Motor*/
_iq Data_Motor_TarSpeed = _IQ(20); //目标基础速度
// 编码器数组映射修正：[0]->右电机(PB11/PB10), [1]->左电机(PB9/PB8)
int16_t Data_MotorEncoder[2] = {0}; //编码器计数数组：[0]=右电机, [1]=左电机
MOTOR_Def_t *Motor[2] = {&Motor_Font_Left, &Motor_Font_Right}; //电机实例

/*Data Tracker*/
uint8_t Data_Tracker_Input[8] = {TRACK_OFF}; //循迹模块的输入值
_iq Data_Tracker_Offset = _IQ(0); //循迹偏差
PID_Def_t Data_Tracker_PID; //转向环PID



/*Test*/
bool Flag_LED = false;


void Task_Key(void *para);
void Task_LED(void *para);
void Task_Motor_PID(void *para);
void Task_Seriwal(void *para);
void Task_OLED(void *para);
void Task_Tracker(void *para);
void Task_AutoRecover(void *para);

void Task_Init(void)
{
    Motor_Start(); //开启电机
    OLED_Init(); //OLED初始化

    Interrupt_Init(); //中断初始化

    Task_Add("Motor", Task_Motor_PID, 20, NULL, 0);
    Task_Add("Tracker", Task_Tracker, 10, NULL, 1);
    Task_Add("Key", Task_Key, 20, NULL, 3);
    Task_Add("OLED", Task_OLED, 30, NULL, 2);
    // Task_Add("AutoRecover", Task_AutoRecover, 10, NULL, 4);
}

//空闲任务函数
void Task_IdleFunction(void)
{
    if (!enable_group1_irq)
    {
        static uint16_t CNT = 0;
        if (++CNT == 5000)
        {
            CNT = 0;
        }
    }
}

// OLED显示任务 - 摄像头状态监控
void Task_OLED(void *para)
{

    // 第三行：电机速度状态（保留原有功能）
    OLED_Printf(0, 16*2, 8, "SL:%4.2f SR:%4.2f",Motor_Font_Left.Motor_PID_Instance.Target,
                                         Motor_Font_Right.Motor_PID_Instance.Target);

    OLED_Printf(0, 16*3, 8, "SL:%4.2f SR:%4.2f",Motor_Font_Left.Motor_PID_Instance.Acutal_Now,
                                         Motor_Font_Right.Motor_PID_Instance.Acutal_Now);
}

//按键 20ms
void Task_Key(void *para)
{
    static uint8_t Key_Old;
    uint8_t Key_Temp = Key_Read();
    uint8_t Key_Val = (Key_Temp ^ Key_Old) & Key_Temp;
    Key_Old = Key_Temp;

    if (Key_Val==2)
    {
        Data_Motor_TarSpeed+=5*16777216;
        // LED_BOARD_TOGGLE();

    }
    else if(Key_Val==3)
    {
        LED_BOARD_TOGGLE();
    }
}



//电机PID调控 50ms
void Task_Motor_PID(void *para)
{
    //获取电机速度
    for (uint8_t i = 0; i < 2; i++)
    {
        Motor_GetSpeed(Motor[i], 20);
    }

    //差速转向控制 - 偏差乘以转向系数
    _iq Steering_Adjustment = _IQmpy(Data_Tracker_Offset, _IQ(INDEX));

    // 左轮：偏右时减速，偏左时加速
    _iq Left_Speed = Data_Motor_TarSpeed - Steering_Adjustment;
    // 右轮：偏右时加速，偏左时减速
    _iq Right_Speed = Data_Motor_TarSpeed + Steering_Adjustment;

    // 设置目标速度
    Motor_Font_Left.Motor_PID_Instance.Target = _IQtoF(Left_Speed);
    Motor_Font_Right.Motor_PID_Instance.Target = _IQtoF(Right_Speed);

    //PID 计算
    for (uint8_t i = 0; i < 2; i++)
    {
        PID_SProsc(&Motor[i]->Motor_PID_Instance);
    }
    // Motor_Font_Left.Motor_PID_Instance.Out = 60;  // 右电机30%速度
    // Motor_Font_Right.Motor_PID_Instance.Out = 60;  // 右电机30%速度
    // 设置电机PWM输出
    for (uint8_t i = 0; i < 2; i++)
    {
        float output = Motor[i]->Motor_PID_Instance.Out;
        Motor_SetDuty(Motor[i], output);
    }

}

//灰度传感器读取、计算偏差 20ms
void Task_Tracker(void *para)
{
    const _iq Filter_Value = _IQ(0.7); //滤波系数
    _iq Temp = _IQ(0); 
    bool res = Tracker_Read(Data_Tracker_Input, &Temp); 
    if (res == true)
    {
        Data_Tracker_Offset = _IQmpy(Temp, Filter_Value) + _IQmpy((_IQ(1) - Filter_Value), Data_Tracker_Offset);
    }
}



void Task_AutoRecover(void *para)
{
    static uint32_t line_found_time = 0;

    extern uint8_t ret;  // 来自Tracker.c的全局变量
    bool tracker_lost = (ret == 0 || ret == 255);  // 失线检测

    // OLED_Printf(0, 16*3, 8, "current_path:%d",current_path);

    if (tracker_lost) {
        lost_time++;
        line_found_time = 0;

        // 简化的失线处理 - 移除角度控制，专注于循迹恢复
        if (lost_time > 20) {
            // 简单的路径状态管理，不依赖角度控制
            switch (current_path) {
                case 0:  // A到C段失线
                    current_path = 1;
                    break;
                case 1:  // 继续寻线
                    current_path = 2;
                    break;
                case 2:  // B到D段失线
                    current_path = 3;
                    break;
                case 3:  // 继续寻线
                    break;
                default:
                    break;
            }
        }
    } else {
        // 重新检测到线
        lost_time = 0;
        line_found_time++;

        // 稳定检测到线50ms后更新路径状态
        if (line_found_time > 10) {
            // 简化的路径状态更新
            if (current_path == 1) {
                current_path = 2;  // C->B完成，开始B->D
            } else if (current_path == 3) {
                current_path = 4;  // D->A完成，任务结束
                Data_Motor_TarSpeed = _IQ(0);  // 停车
            }
        }
    }
}

