******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Thu Jul 31 17:29:05 2025

OUTPUT FILE NAME:   <TI_CAR1.4.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00001bcd


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000021f0  0001de10  R  X
  SRAM                  20200000   00008000  000008e4  0000771c  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000021f0   000021f0    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00002090   00002090    r-x .text
  00002150    00002150    00000048   00000048    r-- .rodata
  00002198    00002198    00000058   00000058    r-- .cinit
20200000    20200000    000006e4   00000000    rw-
  20200000    20200000    00000400   00000000    rw- .sysmem
  20200400    20200400    00000234   00000000    rw- .bss
  20200634    20200634    000000b0   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00002090     
                  000000c0    000001d0     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000290    000001b0     Task.o (.text.Task_Start)
                  00000440    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000005d2    00000002     --HOLE-- [fill = 0]
                  000005d4    00000144     PID.o (.text.PID_SProsc)
                  00000718    00000134     libc.a : qsort.c.obj (.text.qsort)
                  0000084c    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00000958    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00000a5c    000000e8     Task_App.o (.text.Task_Motor_PID)
                  00000b44    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00000c28    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00000d04    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00000ddc    000000d4     Motor.o (.text.Motor_SetDuty)
                  00000eb0    000000b4     Task.o (.text.Task_Add)
                  00000f64    000000b0     Interrupt.o (.text.GROUP1_IRQHandler)
                  00001014    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorAFront_init)
                  000010a0    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorBFront_init)
                  0000112c    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000011b8    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  0000123c    0000007c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000012b8    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00001334    0000000c     SysTick.o (.text.Sys_GetTick)
                  00001340    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  000013b4    00000068     Motor.o (.text.Motor_Start)
                  0000141c    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00001480    00000062     libclang_rt.builtins.a : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  000014e2    00000002     --HOLE-- [fill = 0]
                  000014e4    00000060     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00001544    00000054     Motor.o (.text.CalculateDutyValue)
                  00001598    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000015ec    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  0000163c    00000048     Motor.o (.text.Motor_GetSpeed)
                  00001684    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000016c8    00000044     Motor.o (.text.SetPWMValue)
                  0000170c    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  0000174e    00000002     --HOLE-- [fill = 0]
                  00001750    00000040     Interrupt.o (.text.Interrupt_Init)
                  00001790    00000040     libclang_rt.builtins.a : extendsfdf2.S.obj (.text.__extendsfdf2)
                  000017d0    0000003e     Task.o (.text.Task_CMP)
                  0000180e    00000002     --HOLE-- [fill = 0]
                  00001810    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  0000184c    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00001888    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  000018c4    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00001900    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  0000193c    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00001976    00000002     --HOLE-- [fill = 0]
                  00001978    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  000019b2    00000002     --HOLE-- [fill = 0]
                  000019b4    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000019e8    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00001a1c    00000034     Task_App.o (.text.Task_IdleFunction)
                  00001a50    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  00001a80    0000002c              : _IQNmpy.o (.text._IQ24mpy)
                  00001aac    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00001ad8    0000002a     PID.o (.text.PID_Init)
                  00001b02    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00001b2a    00000002     --HOLE-- [fill = 0]
                  00001b2c    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00001b54    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00001b7c    00000028     SysTick.o (.text.SysTick_Increasment)
                  00001ba4    00000028     Task_App.o (.text.Task_Init)
                  00001bcc    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00001bf4    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00001c1a    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00001c40    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00001c64    00000022     PID.o (.text.PID_SetParams)
                  00001c86    00000002     --HOLE-- [fill = 0]
                  00001c88    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00001ca8    00000020     main.o (.text.main)
                  00001cc8    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00001ce4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00001d00    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00001d1c    0000001c     Interrupt.o (.text.DL_GPIO_enableInterrupt)
                  00001d38    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00001d54    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00001d70    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00001d8c    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00001da8    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00001dc4    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00001de0    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00001dfc    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00001e18    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00001e34    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00001e4c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00001e64    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00001e7c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00001e94    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00001eac    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00001ec4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00001edc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00001ef4    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00001f0c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00001f24    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00001f3c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00001f54    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00001f6c    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00001f84    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00001f9c    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00001fb4    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00001fcc    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00001fe4    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00001ffc    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00002014    00000018     Motor.o (.text.DL_Timer_startCounter)
                  0000202c    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00002042    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00002058    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  0000206c    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00002080    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00002094    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  000020a8    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  000020bc    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  000020ce    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000020e0    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  000020f0    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00002100    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00002110    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000211a    00000008     Interrupt.o (.text.SysTick_Handler)
                  00002122    00000002     --HOLE-- [fill = 0]
                  00002124    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  0000212c    00000006     libc.a : exit.c.obj (.text:abort)
                  00002132    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00002136    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  0000213a    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  0000213e    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00002142    0000000e     --HOLE-- [fill = 0]

.cinit     0    00002198    00000058     
                  00002198    00000034     (.cinit..data.load) [load image, compression = lzss]
                  000021cc    0000000c     (__TI_handler_table)
                  000021d8    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000021e0    00000010     (__TI_cinit_table)

.rodata    0    00002150    00000048     
                  00002150    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00002178    00000008     ti_msp_dl_config.o (.rodata.gMotorAFrontConfig)
                  00002180    00000008     ti_msp_dl_config.o (.rodata.gMotorBFrontConfig)
                  00002188    00000006     Task_App.o (.rodata.str1.3743034515018940988.1)
                  0000218e    00000003     ti_msp_dl_config.o (.rodata.gMotorAFrontClockConfig)
                  00002191    00000003     ti_msp_dl_config.o (.rodata.gMotorBFrontClockConfig)
                  00002194    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00002196    00000002     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.sysmem    0    20200000    00000400     UNINITIALIZED
                  20200000    00000010     libc.a : memory.c.obj (.sysmem)
                  20200010    000003f0     --HOLE--

.bss       0    20200400    00000234     UNINITIALIZED
                  20200400    000000f0     Task.o (.bss.Task_Schedule)
                  202004f0    000000a0     (.common:gMotorAFrontBackup)
                  20200590    000000a0     (.common:gMotorBFrontBackup)
                  20200630    00000004     (.common:ExISR_Flag)

.data      0    20200634    000000b0     UNINITIALIZED
                  20200634    00000048     Motor.o (.data.Motor_Font_Left)
                  2020067c    00000048     Motor.o (.data.Motor_Font_Right)
                  202006c4    00000008     Task_App.o (.data.Motor)
                  202006cc    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202006d0    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  202006d4    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  202006d8    00000004     SysTick.o (.data.delayTick)
                  202006dc    00000004     SysTick.o (.data.uwTick)
                  202006e0    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  202006e2    00000001     Task.o (.data.Task_Num)
                  202006e3    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             2462   64        320    
       startup_mspm0g350x_ticlang.o   8      192       0      
       main.o                         32     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         2502   256       320    
                                                              
    .\APP\Src\
       Interrupt.o                    422    0         5      
       Task_App.o                     324    6         22     
    +--+------------------------------+------+---------+---------+
       Total:                         746    6         27     
                                                              
    .\BSP\Src\
       Task.o                         674    0         241    
       Motor.o                        564    0         144    
       PID.o                          400    0         0      
       SysTick.o                      52     0         8      
    +--+------------------------------+------+---------+---------+
       Total:                         1690   0         393    
                                                              
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388    0         0      
       dl_timer.o                     356    0         0      
       dl_i2c.o                       38     0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         792    0         0      
                                                              
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/iqmath/lib/ticlang/m0p/rts/mspm0g1x0x_g3x0x/iqmath.a
       _IQNtoF.o                      48     0         0      
       _IQNmpy.o                      44     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         92     0         0      
                                                              
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       qsort.c.obj                    308    0         0      
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       memcpy16.S.obj                 18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         600    0         0      
                                                              
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   402    0         0      
       divdf3.S.obj                   268    0         0      
       muldf3.S.obj                   228    0         0      
       addsf3.S.obj                   216    0         0      
       mulsf3.S.obj                   140    0         0      
       comparesf2.S.obj               118    0         0      
       truncdfsf2.S.obj               116    0         0      
       aeabi_fcmp.S.obj               98     0         0      
       fixunsdfsi.S.obj               66     0         0      
       extendsfdf2.S.obj              64     0         0      
       floatsisf.S.obj                60     0         0      
       muldsi3.S.obj                  58     0         0      
       floatunsidf.S.obj              36     0         0      
       aeabi_memcpy.S.obj             8      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         1878   0         0      
                                                              
       Heap:                          0      0         1024   
       Stack:                         0      0         512    
       Linker Generated:              0      88        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   8304   350       2276   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000021e0 records: 2, size/record: 8, table size: 16
	.data: load addr=00002198, load size=00000034 bytes, run addr=20200634, run size=000000b0 bytes, compression=lzss
	.bss: load addr=000021d8, load size=00000008 bytes, run addr=20200400, run size=00000234 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000021cc records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00002133  ADC0_IRQHandler                      
00002133  ADC1_IRQHandler                      
00002133  AES_IRQHandler                       
00002136  C$$EXIT                              
00002133  CANFD0_IRQHandler                    
00002133  DAC0_IRQHandler                      
00002111  DL_Common_delayCycles                
00001c1b  DL_I2C_setClockConfig                
00000c29  DL_SYSCTL_configSYSPLL               
0000141d  DL_SYSCTL_setHFCLKSourceHFXTParams   
00001685  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00000959  DL_Timer_initFourCCPWMMode           
00001dfd  DL_Timer_setCaptCompUpdateMethod     
00001ffd  DL_Timer_setCaptureCompareOutCtl     
000020f1  DL_Timer_setCaptureCompareValue      
00001e19  DL_Timer_setClockConfig              
00002133  DMA_IRQHandler                       
202006cc  Data_MotorEncoder                    
202006d0  Data_Motor_TarSpeed                  
202006d4  Data_Tracker_Offset                  
00002133  Default_Handler                      
20200630  ExISR_Flag                           
00002133  GROUP0_IRQHandler                    
00000f65  GROUP1_IRQHandler                    
00002137  HOSTexit                             
00002133  HardFault_Handler                    
00002133  I2C0_IRQHandler                      
00002133  I2C1_IRQHandler                      
00001751  Interrupt_Init                       
202006c4  Motor                                
20200634  Motor_Font_Left                      
2020067c  Motor_Font_Right                     
0000163d  Motor_GetSpeed                       
00000ddd  Motor_SetDuty                        
000013b5  Motor_Start                          
00002133  NMI_Handler                          
00001ad9  PID_Init                             
000005d5  PID_SProsc                           
00001c65  PID_SetParams                        
00002133  PendSV_Handler                       
00002133  RTC_IRQHandler                       
0000213b  Reset_Handler                        
00002133  SPI0_IRQHandler                      
00002133  SPI1_IRQHandler                      
00002133  SVC_Handler                          
000000c1  SYSCFG_DL_GPIO_init                  
000014e5  SYSCFG_DL_I2C_OLED_init              
00001015  SYSCFG_DL_MotorAFront_init           
000010a1  SYSCFG_DL_MotorBFront_init           
00001599  SYSCFG_DL_SYSCTL_init                
00002101  SYSCFG_DL_SYSTICK_init               
000019e9  SYSCFG_DL_init                       
0000123d  SYSCFG_DL_initPower                  
0000211b  SysTick_Handler                      
00001b7d  SysTick_Increasment                  
00001335  Sys_GetTick                          
00002133  TIMA0_IRQHandler                     
00002133  TIMA1_IRQHandler                     
00002133  TIMG0_IRQHandler                     
00002133  TIMG12_IRQHandler                    
00002133  TIMG6_IRQHandler                     
00002133  TIMG7_IRQHandler                     
00002133  TIMG8_IRQHandler                     
000020bd  TI_memcpy_small                      
00000eb1  Task_Add                             
00001a1d  Task_IdleFunction                    
00001ba5  Task_Init                            
00000a5d  Task_Motor_PID                       
00000291  Task_Start                           
00002133  UART0_IRQHandler                     
00002133  UART1_IRQHandler                     
00002133  UART2_IRQHandler                     
00002133  UART3_IRQHandler                     
00001a81  _IQ24mpy                             
00001a51  _IQ24toF                             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000400  __SYSMEM_SIZE                        
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
000021e0  __TI_CINIT_Base                      
000021f0  __TI_CINIT_Limit                     
000021f0  __TI_CINIT_Warm                      
000021cc  __TI_Handler_Table_Base              
000021d8  __TI_Handler_Table_Limit             
00001901  __TI_auto_init_nobinit_nopinit       
000012b9  __TI_decompress_lzss                 
000020cf  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00002043  __TI_zero_init_nomemset              
0000044b  __adddf3                             
00000d0f  __addsf3                             
00001341  __aeabi_d2f                          
0000170d  __aeabi_d2uiz                        
0000044b  __aeabi_dadd                         
0000084d  __aeabi_ddiv                         
00000b45  __aeabi_dmul                         
00000441  __aeabi_dsub                         
00001791  __aeabi_f2d                          
00000d0f  __aeabi_fadd                         
00001481  __aeabi_fcmpeq                       
000014bd  __aeabi_fcmpge                       
000014d1  __aeabi_fcmpgt                       
000014a9  __aeabi_fcmple                       
00001495  __aeabi_fcmplt                       
0000112d  __aeabi_fmul                         
00000d05  __aeabi_fsub                         
00001889  __aeabi_i2f                          
00002125  __aeabi_memcpy                       
00002125  __aeabi_memcpy4                      
00002125  __aeabi_memcpy8                      
00001c41  __aeabi_ui2d                         
ffffffff  __binit__                            
0000193d  __cmpsf2                             
0000084d  __divdf3                             
0000193d  __eqsf2                              
00001791  __extendsfdf2                        
0000170d  __fixunsdfsi                         
00001889  __floatsisf                          
00001c41  __floatunsidf                        
000018c5  __gesf2                              
000018c5  __gtsf2                              
0000193d  __lesf2                              
0000193d  __ltsf2                              
UNDEFED   __mpu_init                           
00000b45  __muldf3                             
00001979  __muldsi3                            
0000112d  __mulsf3                             
0000193d  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00000441  __subdf3                             
00000d05  __subsf3                             
00001341  __truncdfsf2                         
00001bcd  _c_int00_noargs                      
20200000  _sys_memory                          
UNDEFED   _system_post_cinit                   
0000213f  _system_pre_init                     
0000212d  abort                                
ffffffff  binit                                
202006d8  delayTick                            
202006e3  enable_group1_irq                    
202004f0  gMotorAFrontBackup                   
20200590  gMotorBFrontBackup                   
00000000  interruptVectors                     
00001ca9  main                                 
00000719  qsort                                
202006dc  uwTick                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  SYSCFG_DL_GPIO_init                  
00000200  __STACK_SIZE                         
00000291  Task_Start                           
00000400  __SYSMEM_SIZE                        
00000441  __aeabi_dsub                         
00000441  __subdf3                             
0000044b  __adddf3                             
0000044b  __aeabi_dadd                         
000005d5  PID_SProsc                           
00000719  qsort                                
0000084d  __aeabi_ddiv                         
0000084d  __divdf3                             
00000959  DL_Timer_initFourCCPWMMode           
00000a5d  Task_Motor_PID                       
00000b45  __aeabi_dmul                         
00000b45  __muldf3                             
00000c29  DL_SYSCTL_configSYSPLL               
00000d05  __aeabi_fsub                         
00000d05  __subsf3                             
00000d0f  __addsf3                             
00000d0f  __aeabi_fadd                         
00000ddd  Motor_SetDuty                        
00000eb1  Task_Add                             
00000f65  GROUP1_IRQHandler                    
00001015  SYSCFG_DL_MotorAFront_init           
000010a1  SYSCFG_DL_MotorBFront_init           
0000112d  __aeabi_fmul                         
0000112d  __mulsf3                             
0000123d  SYSCFG_DL_initPower                  
000012b9  __TI_decompress_lzss                 
00001335  Sys_GetTick                          
00001341  __aeabi_d2f                          
00001341  __truncdfsf2                         
000013b5  Motor_Start                          
0000141d  DL_SYSCTL_setHFCLKSourceHFXTParams   
00001481  __aeabi_fcmpeq                       
00001495  __aeabi_fcmplt                       
000014a9  __aeabi_fcmple                       
000014bd  __aeabi_fcmpge                       
000014d1  __aeabi_fcmpgt                       
000014e5  SYSCFG_DL_I2C_OLED_init              
00001599  SYSCFG_DL_SYSCTL_init                
0000163d  Motor_GetSpeed                       
00001685  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
0000170d  __aeabi_d2uiz                        
0000170d  __fixunsdfsi                         
00001751  Interrupt_Init                       
00001791  __aeabi_f2d                          
00001791  __extendsfdf2                        
00001889  __aeabi_i2f                          
00001889  __floatsisf                          
000018c5  __gesf2                              
000018c5  __gtsf2                              
00001901  __TI_auto_init_nobinit_nopinit       
0000193d  __cmpsf2                             
0000193d  __eqsf2                              
0000193d  __lesf2                              
0000193d  __ltsf2                              
0000193d  __nesf2                              
00001979  __muldsi3                            
000019e9  SYSCFG_DL_init                       
00001a1d  Task_IdleFunction                    
00001a51  _IQ24toF                             
00001a81  _IQ24mpy                             
00001ad9  PID_Init                             
00001b7d  SysTick_Increasment                  
00001ba5  Task_Init                            
00001bcd  _c_int00_noargs                      
00001c1b  DL_I2C_setClockConfig                
00001c41  __aeabi_ui2d                         
00001c41  __floatunsidf                        
00001c65  PID_SetParams                        
00001ca9  main                                 
00001dfd  DL_Timer_setCaptCompUpdateMethod     
00001e19  DL_Timer_setClockConfig              
00001ffd  DL_Timer_setCaptureCompareOutCtl     
00002043  __TI_zero_init_nomemset              
000020bd  TI_memcpy_small                      
000020cf  __TI_decompress_none                 
000020f1  DL_Timer_setCaptureCompareValue      
00002101  SYSCFG_DL_SYSTICK_init               
00002111  DL_Common_delayCycles                
0000211b  SysTick_Handler                      
00002125  __aeabi_memcpy                       
00002125  __aeabi_memcpy4                      
00002125  __aeabi_memcpy8                      
0000212d  abort                                
00002133  ADC0_IRQHandler                      
00002133  ADC1_IRQHandler                      
00002133  AES_IRQHandler                       
00002133  CANFD0_IRQHandler                    
00002133  DAC0_IRQHandler                      
00002133  DMA_IRQHandler                       
00002133  Default_Handler                      
00002133  GROUP0_IRQHandler                    
00002133  HardFault_Handler                    
00002133  I2C0_IRQHandler                      
00002133  I2C1_IRQHandler                      
00002133  NMI_Handler                          
00002133  PendSV_Handler                       
00002133  RTC_IRQHandler                       
00002133  SPI0_IRQHandler                      
00002133  SPI1_IRQHandler                      
00002133  SVC_Handler                          
00002133  TIMA0_IRQHandler                     
00002133  TIMA1_IRQHandler                     
00002133  TIMG0_IRQHandler                     
00002133  TIMG12_IRQHandler                    
00002133  TIMG6_IRQHandler                     
00002133  TIMG7_IRQHandler                     
00002133  TIMG8_IRQHandler                     
00002133  UART0_IRQHandler                     
00002133  UART1_IRQHandler                     
00002133  UART2_IRQHandler                     
00002133  UART3_IRQHandler                     
00002136  C$$EXIT                              
00002137  HOSTexit                             
0000213b  Reset_Handler                        
0000213f  _system_pre_init                     
000021cc  __TI_Handler_Table_Base              
000021d8  __TI_Handler_Table_Limit             
000021e0  __TI_CINIT_Base                      
000021f0  __TI_CINIT_Limit                     
000021f0  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  _sys_memory                          
202004f0  gMotorAFrontBackup                   
20200590  gMotorBFrontBackup                   
20200630  ExISR_Flag                           
20200634  Motor_Font_Left                      
2020067c  Motor_Font_Right                     
202006c4  Motor                                
202006cc  Data_MotorEncoder                    
202006d0  Data_Motor_TarSpeed                  
202006d4  Data_Tracker_Offset                  
202006d8  delayTick                            
202006dc  uwTick                               
202006e3  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[161 symbols]
