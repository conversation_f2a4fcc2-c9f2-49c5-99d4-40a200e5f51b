******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Thu Jul 31 20:29:18 2025

OUTPUT FILE NAME:   <TI_CAR1.4.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00003f05


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00005208  0001adf8  R  X
  SRAM                  20200000   00008000  000008f2  0000770e  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00005208   00005208    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00004700   00004700    r-x .text
  000047c0    000047c0    000009f0   000009f0    r-- .rodata
  000051b0    000051b0    00000058   00000058    r-- .cinit
20200000    20200000    000006f4   00000000    rw-
  20200000    20200000    00000400   00000000    rw- .sysmem
  20200400    20200400    00000236   00000000    rw- .bss
  20200638    20200638    000000bc   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00004700     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000220            : _printfi.c.obj (.text._pconv_a)
                  00000cb0    000001dc            : _printfi.c.obj (.text._pconv_g)
                  00000e8c    000001b0     Task.o (.text.Task_Start)
                  0000103c    000001ac     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000011e8    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000137a    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  0000137c    0000015c     Tracker.o (.text.Tracker_Read)
                  000014d8    00000144     PID.o (.text.PID_SProsc)
                  0000161c    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00001758    00000134            : qsort.c.obj (.text.qsort)
                  0000188c    00000130     OLED.o (.text.OLED_ShowChar)
                  000019bc    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00001adc    00000110     OLED.o (.text.OLED_Init)
                  00001bec    0000010c     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  00001cf8    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001e04    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00001f08    000000e8     Task_App.o (.text.Task_Motor_PID)
                  00001ff0    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000020d4    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  000021b0    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00002288    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00002360    000000d4     Motor.o (.text.Motor_SetDuty)
                  00002434    000000b8     Task_App.o (.text.Task_Key)
                  000024ec    000000b4     Task.o (.text.Task_Add)
                  000025a0    000000b0     Interrupt.o (.text.GROUP1_IRQHandler)
                  00002650    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  000026f2    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  000026f4    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  0000278c    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorAFront_init)
                  00002818    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorBFront_init)
                  000028a4    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00002930    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000029bc    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00002a40    00000080     Task_App.o (.text.Task_OLED)
                  00002ac0    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002b3c    00000078     Motor.o (.text.Motor_Start)
                  00002bb4    00000078     Task_App.o (.text.Task_Init)
                  00002c2c    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00002ca0    00000074                            : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00002d14    00000074     SysTick.o (.text.delay_us)
                  00002d88    0000006e     OLED.o (.text.OLED_ShowString)
                  00002df6    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  00002e60    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00002ec8    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00002f2e    00000002     --HOLE-- [fill = 0]
                  00002f30    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00002f94    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00002ff6    00000002     --HOLE-- [fill = 0]
                  00002ff8    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  0000305a    00000002     --HOLE-- [fill = 0]
                  0000305c    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  000030bc    00000060     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  0000311c    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  0000317a    00000002     --HOLE-- [fill = 0]
                  0000317c    0000005c     Task_App.o (.text.Task_Tracker)
                  000031d8    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00003234    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  00003290    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  000032e8    00000058            : _printfi.c.obj (.text._pconv_f)
                  00003340    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00003396    00000002     --HOLE-- [fill = 0]
                  00003398    00000054     Motor.o (.text.CalculateDutyValue)
                  000033ec    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00003440    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00003492    00000002     --HOLE-- [fill = 0]
                  00003494    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  000034e4    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00003534    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  00003580    0000004c     Key_Led.o (.text.Key_Read)
                  000035cc    0000004c     OLED.o (.text.OLED_Printf)
                  00003618    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  00003664    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  000036ae    00000002     --HOLE-- [fill = 0]
                  000036b0    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  000036fa    00000048     Motor.o (.text.Motor_GetSpeed)
                  00003742    00000002     --HOLE-- [fill = 0]
                  00003744    00000048     OLED.o (.text.mspm0_i2c_disable)
                  0000378c    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000037d0    00000044     Motor.o (.text.SetPWMValue)
                  00003814    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  00003856    00000002     --HOLE-- [fill = 0]
                  00003858    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00003898    00000040     Interrupt.o (.text.Interrupt_Init)
                  000038d8    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00003918    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00003958    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00003998    0000003e     Task.o (.text.Task_CMP)
                  000039d6    00000002     --HOLE-- [fill = 0]
                  000039d8    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00003a14    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00003a50    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00003a8c    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  00003ac8    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00003b04    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00003b40    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00003b7c    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00003bb6    00000002     --HOLE-- [fill = 0]
                  00003bb8    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00003bf2    00000002     --HOLE-- [fill = 0]
                  00003bf4    00000038     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00003c2c    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00003c64    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003c98    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003ccc    00000034     Task_App.o (.text.Task_IdleFunction)
                  00003d00    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  00003d30    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00003d60    0000002c     iqmath.a : _IQNmpy.o (.text._IQ24mpy)
                  00003d8c    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00003db8    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00003de4    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00003e10    0000002a     PID.o (.text.PID_Init)
                  00003e3a    00000028     OLED.o (.text.DL_Common_updateReg)
                  00003e62    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00003e8a    00000002     --HOLE-- [fill = 0]
                  00003e8c    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00003eb4    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00003edc    00000028     SysTick.o (.text.SysTick_Increasment)
                  00003f04    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00003f2c    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00003f52    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00003f78    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00003f9c    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00003fc0    00000022     PID.o (.text.PID_SetParams)
                  00003fe2    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00004004    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00004024    00000020     SysTick.o (.text.Delay)
                  00004044    00000020     main.o (.text.main)
                  00004064    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00004082    00000002     --HOLE-- [fill = 0]
                  00004084    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  000040a0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  000040bc    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  000040d8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  000040f4    0000001c     Interrupt.o (.text.DL_GPIO_enableInterrupt)
                  00004110    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  0000412c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00004148    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00004164    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00004180    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  0000419c    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  000041b8    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  000041d4    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  000041f0    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  0000420c    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00004228    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00004240    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00004258    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  00004270    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00004288    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  000042a0    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  000042b8    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  000042d0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  000042e8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00004300    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00004318    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00004330    00000018     Task_App.o (.text.DL_GPIO_setPins)
                  00004348    00000018     Tracker.o (.text.DL_GPIO_setPins)
                  00004360    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00004378    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00004390    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  000043a8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  000043c0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  000043d8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  000043f0    00000018     OLED.o (.text.DL_I2C_enablePower)
                  00004408    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00004420    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  00004438    00000018     OLED.o (.text.DL_I2C_reset)
                  00004450    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00004468    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00004480    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00004498    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  000044b0    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000044c8    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000044e0    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000044f8    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00004510    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00004528    00000018     libc.a : vsprintf.c.obj (.text._outs)
                  00004540    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00004556    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  0000456c    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  00004582    00000016     OLED.o (.text.DL_GPIO_readPins)
                  00004598    00000016     Tracker.o (.text.DL_GPIO_readPins)
                  000045ae    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000045c4    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  000045d8    00000014     Task_App.o (.text.DL_GPIO_clearPins)
                  000045ec    00000014     Tracker.o (.text.DL_GPIO_clearPins)
                  00004600    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00004614    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  00004628    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  0000463c    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00004650    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00004664    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00004678    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  0000468c    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  000046a0    00000012            : memcpy16.S.obj (.text:TI_memcpy_small)
                  000046b2    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000046c4    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  000046d4    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  000046e4    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  000046f4    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00004704    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00004712    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00004720    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  0000472e    00000002     --HOLE-- [fill = 0]
                  00004730    0000000c     SysTick.o (.text.Sys_GetTick)
                  0000473c    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00004746    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00004750    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00004760    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  0000476a    0000000a            : vsprintf.c.obj (.text._outc)
                  00004774    00000008     Interrupt.o (.text.SysTick_Handler)
                  0000477c    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00004784    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  0000478c    00000006     libc.a : exit.c.obj (.text:abort)
                  00004792    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00004796    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  0000479a    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  0000479e    00000002     --HOLE-- [fill = 0]
                  000047a0    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  000047b0    00000004            : pre_init.c.obj (.text._system_pre_init)
                  000047b4    0000000c     --HOLE-- [fill = 0]

.cinit     0    000051b0    00000058     
                  000051b0    00000034     (.cinit..data.load) [load image, compression = lzss]
                  000051e4    0000000c     (__TI_handler_table)
                  000051f0    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000051f8    00000010     (__TI_cinit_table)

.rodata    0    000047c0    000009f0     
                  000047c0    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  00004db0    00000228     OLED_Font.o (.rodata.asc2_0806)
                  00004fd8    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  00004fe0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  000050e1    00000041     iqmath.a : _IQNtables.o (.rodata._IQ6div_lookup)
                  00005122    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00005124    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  0000514c    00000012     Task_App.o (.rodata.str1.16020955549137178199.1)
                  0000515e    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  0000516f    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00005180    00000008     ti_msp_dl_config.o (.rodata.gMotorAFrontConfig)
                  00005188    00000008     ti_msp_dl_config.o (.rodata.gMotorBFrontConfig)
                  00005190    00000008     Task_App.o (.rodata.str1.12629676409056169537.1)
                  00005198    00000006     Task_App.o (.rodata.str1.3743034515018940988.1)
                  0000519e    00000005     Task_App.o (.rodata.str1.8896853068034818020.1)
                  000051a3    00000004     Task_App.o (.rodata.str1.10635198597896025474.1)
                  000051a7    00000003     ti_msp_dl_config.o (.rodata.gMotorAFrontClockConfig)
                  000051aa    00000003     ti_msp_dl_config.o (.rodata.gMotorBFrontClockConfig)
                  000051ad    00000003     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.sysmem    0    20200000    00000400     UNINITIALIZED
                  20200000    00000010     libc.a : memory.c.obj (.sysmem)
                  20200010    000003f0     --HOLE--

.bss       0    20200400    00000236     UNINITIALIZED
                  20200400    000000f0     Task.o (.bss.Task_Schedule)
                  202004f0    000000a0     (.common:gMotorAFrontBackup)
                  20200590    000000a0     (.common:gMotorBFrontBackup)
                  20200630    00000004     (.common:ExISR_Flag)
                  20200634    00000001     Task_App.o (.bss.Task_Key.Key_Old)
                  20200635    00000001     (.common:ret)

.data      0    20200638    000000bc     UNINITIALIZED
                  20200638    00000048     Motor.o (.data.Motor_Font_Left)
                  20200680    00000048     Motor.o (.data.Motor_Font_Right)
                  202006c8    00000008     Task_App.o (.data.Data_Tracker_Input)
                  202006d0    00000008     Task_App.o (.data.Motor)
                  202006d8    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202006dc    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  202006e0    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  202006e4    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202006e8    00000004     SysTick.o (.data.delayTick)
                  202006ec    00000004     SysTick.o (.data.uwTick)
                  202006f0    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  202006f2    00000001     Task.o (.data.Task_Num)
                  202006f3    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             2742    72        320    
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         32      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2782    264       320    
                                                               
    .\APP\Src\
       Task_App.o                     852     41        31     
       Interrupt.o                    422     0         5      
    +--+------------------------------+-------+---------+---------+
       Total:                         1274    41        36     
                                                               
    .\BSP\Src\
       OLED_Font.o                    0       2072      0      
       OLED.o                         1858    0         0      
       Task.o                         674     0         241    
       Motor.o                        580     0         144    
       Tracker.o                      414     0         1      
       PID.o                          400     0         0      
       SysTick.o                      200     0         8      
       Key_Led.o                      98      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4224    2072      394    
                                                               
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_timer.o                     356     0         0      
       dl_i2c.o                       132     0         0      
       dl_adc12.o                     64      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         950     0         0      
                                                               
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/iqmath/lib/ticlang/m0p/rts/mspm0g1x0x_g3x0x/iqmath.a
       _IQNdiv.o                      268     0         0      
       _IQNtables.o                   0       65        0      
       _IQNtoF.o                      48      0         0      
       _IQNmpy.o                      44      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         360     65        0      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       qsort.c.obj                    308     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       vsprintf.c.obj                 78      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5736    291       4      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       fixunsdfsi.S.obj               66      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsidf.S.obj              36      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memset.S.obj             14      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2802    0         0      
                                                               
       Heap:                          0       0         1024   
       Stack:                         0       0         512    
       Linker Generated:              0       88        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   18132   2821      2290   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000051f8 records: 2, size/record: 8, table size: 16
	.data: load addr=000051b0, load size=00000034 bytes, run addr=20200638, run size=000000bc bytes, compression=lzss
	.bss: load addr=000051f0, load size=00000008 bytes, run addr=20200400, run size=00000236 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000051e4 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   000011e9     00004750     0000474e   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00003f05     000047a0     0000479a   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[2 trampolines]
[2 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00004793  ADC0_IRQHandler                      
00004793  ADC1_IRQHandler                      
00004793  AES_IRQHandler                       
00004796  C$$EXIT                              
00004793  CANFD0_IRQHandler                    
00004793  DAC0_IRQHandler                      
00003859  DL_ADC12_setClockConfig              
0000473d  DL_Common_delayCycles                
0000311d  DL_I2C_fillControllerTXFIFO          
00003f53  DL_I2C_setClockConfig                
000020d5  DL_SYSCTL_configSYSPLL               
00002f31  DL_SYSCTL_setHFCLKSourceHFXTParams   
0000378d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00001e05  DL_Timer_initFourCCPWMMode           
000041f1  DL_Timer_setCaptCompUpdateMethod     
000044f9  DL_Timer_setCaptureCompareOutCtl     
000046d5  DL_Timer_setCaptureCompareValue      
0000420d  DL_Timer_setClockConfig              
00004793  DMA_IRQHandler                       
202006d8  Data_MotorEncoder                    
202006dc  Data_Motor_TarSpeed                  
202006c8  Data_Tracker_Input                   
202006e0  Data_Tracker_Offset                  
00004793  Default_Handler                      
00004025  Delay                                
20200630  ExISR_Flag                           
00004793  GROUP0_IRQHandler                    
000025a1  GROUP1_IRQHandler                    
00004797  HOSTexit                             
00004793  HardFault_Handler                    
00004793  I2C0_IRQHandler                      
00004793  I2C1_IRQHandler                      
00002df7  I2C_OLED_Clear                       
00003a8d  I2C_OLED_Set_Pos                     
000026f5  I2C_OLED_WR_Byte                     
0000305d  I2C_OLED_i2c_sda_unlock              
00003899  Interrupt_Init                       
00003581  Key_Read                             
202006d0  Motor                                
20200638  Motor_Font_Left                      
20200680  Motor_Font_Right                     
000036fb  Motor_GetSpeed                       
00002361  Motor_SetDuty                        
00002b3d  Motor_Start                          
00004793  NMI_Handler                          
00001add  OLED_Init                            
000035cd  OLED_Printf                          
0000188d  OLED_ShowChar                        
00002d89  OLED_ShowString                      
00003e11  PID_Init                             
000014d9  PID_SProsc                           
00003fc1  PID_SetParams                        
00004793  PendSV_Handler                       
00004793  RTC_IRQHandler                       
0000479b  Reset_Handler                        
00004793  SPI0_IRQHandler                      
00004793  SPI1_IRQHandler                      
00004793  SVC_Handler                          
00003619  SYSCFG_DL_ADC1_init                  
0000103d  SYSCFG_DL_GPIO_init                  
000030bd  SYSCFG_DL_I2C_OLED_init              
0000278d  SYSCFG_DL_MotorAFront_init           
00002819  SYSCFG_DL_MotorBFront_init           
000033ed  SYSCFG_DL_SYSCTL_init                
000046e5  SYSCFG_DL_SYSTICK_init               
00003bf5  SYSCFG_DL_init                       
000028a5  SYSCFG_DL_initPower                  
00004775  SysTick_Handler                      
00003edd  SysTick_Increasment                  
00004731  Sys_GetTick                          
00004793  TIMA0_IRQHandler                     
00004793  TIMA1_IRQHandler                     
00004793  TIMG0_IRQHandler                     
00004793  TIMG12_IRQHandler                    
00004793  TIMG6_IRQHandler                     
00004793  TIMG7_IRQHandler                     
00004793  TIMG8_IRQHandler                     
000046a1  TI_memcpy_small                      
00004721  TI_memset_small                      
000024ed  Task_Add                             
00003ccd  Task_IdleFunction                    
00002bb5  Task_Init                            
00002435  Task_Key                             
00001f09  Task_Motor_PID                       
00002a41  Task_OLED                            
00000e8d  Task_Start                           
0000317d  Task_Tracker                         
0000137d  Tracker_Read                         
00004793  UART0_IRQHandler                     
00004793  UART1_IRQHandler                     
00004793  UART2_IRQHandler                     
00004793  UART3_IRQHandler                     
00001bed  _IQ24div                             
00003d61  _IQ24mpy                             
00003d01  _IQ24toF                             
000050e1  _IQ6div_lookup                       
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000400  __SYSMEM_SIZE                        
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
000051f8  __TI_CINIT_Base                      
00005208  __TI_CINIT_Limit                     
00005208  __TI_CINIT_Warm                      
000051e4  __TI_Handler_Table_Base              
000051f0  __TI_Handler_Table_Limit             
00003b41  __TI_auto_init_nobinit_nopinit       
00002ac1  __TI_decompress_lzss                 
000046b3  __TI_decompress_none                 
00003291  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
000045af  __TI_zero_init_nomemset              
000011f3  __adddf3                             
00002293  __addsf3                             
00004fe0  __aeabi_ctype_table_                 
00004fe0  __aeabi_ctype_table_C                
00002ca1  __aeabi_d2f                          
000036b1  __aeabi_d2iz                         
00003815  __aeabi_d2uiz                        
000011f3  __aeabi_dadd                         
00002f95  __aeabi_dcmpeq                       
00002fd1  __aeabi_dcmpge                       
00002fe5  __aeabi_dcmpgt                       
00002fbd  __aeabi_dcmple                       
00002fa9  __aeabi_dcmplt                       
00001cf9  __aeabi_ddiv                         
00001ff1  __aeabi_dmul                         
000011e9  __aeabi_dsub                         
202006e4  __aeabi_errno                        
0000477d  __aeabi_errno_addr                   
00003919  __aeabi_f2d                          
00003c2d  __aeabi_f2iz                         
00002293  __aeabi_fadd                         
00002ff9  __aeabi_fcmpeq                       
00003035  __aeabi_fcmpge                       
00003049  __aeabi_fcmpgt                       
00003021  __aeabi_fcmple                       
0000300d  __aeabi_fcmplt                       
00002931  __aeabi_fmul                         
00002289  __aeabi_fsub                         
00003db9  __aeabi_i2d                          
00003ac9  __aeabi_i2f                          
00003341  __aeabi_idiv                         
0000137b  __aeabi_idiv0                        
00003341  __aeabi_idivmod                      
000026f3  __aeabi_ldiv0                        
00004065  __aeabi_llsl                         
00003f9d  __aeabi_lmul                         
00004785  __aeabi_memcpy                       
00004785  __aeabi_memcpy4                      
00004785  __aeabi_memcpy8                      
00004705  __aeabi_memset                       
00004705  __aeabi_memset4                      
00004705  __aeabi_memset8                      
00003f79  __aeabi_ui2d                         
000038d9  __aeabi_uidiv                        
000038d9  __aeabi_uidivmod                     
00004679  __aeabi_uldivmod                     
00004065  __ashldi3                            
ffffffff  __binit__                            
00002e61  __cmpdf2                             
00003b7d  __cmpsf2                             
00001cf9  __divdf3                             
00002e61  __eqdf2                              
00003b7d  __eqsf2                              
00003919  __extendsfdf2                        
000036b1  __fixdfsi                            
00003c2d  __fixsfsi                            
00003815  __fixunsdfsi                         
00003db9  __floatsidf                          
00003ac9  __floatsisf                          
00003f79  __floatunsidf                        
00002c2d  __gedf2                              
00003b05  __gesf2                              
00002c2d  __gtdf2                              
00003b05  __gtsf2                              
00002e61  __ledf2                              
00003b7d  __lesf2                              
00002e61  __ltdf2                              
00003b7d  __ltsf2                              
UNDEFED   __mpu_init                           
00001ff1  __muldf3                             
00003f9d  __muldi3                             
00003bb9  __muldsi3                            
00002931  __mulsf3                             
00002e61  __nedf2                              
00003b7d  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000011e9  __subdf3                             
00002289  __subsf3                             
00002ca1  __truncdfsf2                         
00002651  __udivmoddi4                         
00003f05  _c_int00_noargs                      
20200000  _sys_memory                          
UNDEFED   _system_post_cinit                   
000047b1  _system_pre_init                     
0000478d  abort                                
00004db0  asc2_0806                            
000047c0  asc2_1608                            
00003959  atoi                                 
ffffffff  binit                                
202006e8  delayTick                            
00002d15  delay_us                             
202006f3  enable_group1_irq                    
000031d9  frexp                                
000031d9  frexpl                               
202004f0  gMotorAFrontBackup                   
20200590  gMotorBFrontBackup                   
00000000  interruptVectors                     
000021b1  ldexp                                
000021b1  ldexpl                               
00004045  main                                 
00003fe3  memccpy                              
00001759  qsort                                
20200635  ret                                  
000021b1  scalbn                               
000021b1  scalbnl                              
202006ec  uwTick                               
00003de5  vsprintf                             
000046f5  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000400  __SYSMEM_SIZE                        
00000e8d  Task_Start                           
0000103d  SYSCFG_DL_GPIO_init                  
000011e9  __aeabi_dsub                         
000011e9  __subdf3                             
000011f3  __adddf3                             
000011f3  __aeabi_dadd                         
0000137b  __aeabi_idiv0                        
0000137d  Tracker_Read                         
000014d9  PID_SProsc                           
00001759  qsort                                
0000188d  OLED_ShowChar                        
00001add  OLED_Init                            
00001bed  _IQ24div                             
00001cf9  __aeabi_ddiv                         
00001cf9  __divdf3                             
00001e05  DL_Timer_initFourCCPWMMode           
00001f09  Task_Motor_PID                       
00001ff1  __aeabi_dmul                         
00001ff1  __muldf3                             
000020d5  DL_SYSCTL_configSYSPLL               
000021b1  ldexp                                
000021b1  ldexpl                               
000021b1  scalbn                               
000021b1  scalbnl                              
00002289  __aeabi_fsub                         
00002289  __subsf3                             
00002293  __addsf3                             
00002293  __aeabi_fadd                         
00002361  Motor_SetDuty                        
00002435  Task_Key                             
000024ed  Task_Add                             
000025a1  GROUP1_IRQHandler                    
00002651  __udivmoddi4                         
000026f3  __aeabi_ldiv0                        
000026f5  I2C_OLED_WR_Byte                     
0000278d  SYSCFG_DL_MotorAFront_init           
00002819  SYSCFG_DL_MotorBFront_init           
000028a5  SYSCFG_DL_initPower                  
00002931  __aeabi_fmul                         
00002931  __mulsf3                             
00002a41  Task_OLED                            
00002ac1  __TI_decompress_lzss                 
00002b3d  Motor_Start                          
00002bb5  Task_Init                            
00002c2d  __gedf2                              
00002c2d  __gtdf2                              
00002ca1  __aeabi_d2f                          
00002ca1  __truncdfsf2                         
00002d15  delay_us                             
00002d89  OLED_ShowString                      
00002df7  I2C_OLED_Clear                       
00002e61  __cmpdf2                             
00002e61  __eqdf2                              
00002e61  __ledf2                              
00002e61  __ltdf2                              
00002e61  __nedf2                              
00002f31  DL_SYSCTL_setHFCLKSourceHFXTParams   
00002f95  __aeabi_dcmpeq                       
00002fa9  __aeabi_dcmplt                       
00002fbd  __aeabi_dcmple                       
00002fd1  __aeabi_dcmpge                       
00002fe5  __aeabi_dcmpgt                       
00002ff9  __aeabi_fcmpeq                       
0000300d  __aeabi_fcmplt                       
00003021  __aeabi_fcmple                       
00003035  __aeabi_fcmpge                       
00003049  __aeabi_fcmpgt                       
0000305d  I2C_OLED_i2c_sda_unlock              
000030bd  SYSCFG_DL_I2C_OLED_init              
0000311d  DL_I2C_fillControllerTXFIFO          
0000317d  Task_Tracker                         
000031d9  frexp                                
000031d9  frexpl                               
00003291  __TI_ltoa                            
00003341  __aeabi_idiv                         
00003341  __aeabi_idivmod                      
000033ed  SYSCFG_DL_SYSCTL_init                
00003581  Key_Read                             
000035cd  OLED_Printf                          
00003619  SYSCFG_DL_ADC1_init                  
000036b1  __aeabi_d2iz                         
000036b1  __fixdfsi                            
000036fb  Motor_GetSpeed                       
0000378d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00003815  __aeabi_d2uiz                        
00003815  __fixunsdfsi                         
00003859  DL_ADC12_setClockConfig              
00003899  Interrupt_Init                       
000038d9  __aeabi_uidiv                        
000038d9  __aeabi_uidivmod                     
00003919  __aeabi_f2d                          
00003919  __extendsfdf2                        
00003959  atoi                                 
00003a8d  I2C_OLED_Set_Pos                     
00003ac9  __aeabi_i2f                          
00003ac9  __floatsisf                          
00003b05  __gesf2                              
00003b05  __gtsf2                              
00003b41  __TI_auto_init_nobinit_nopinit       
00003b7d  __cmpsf2                             
00003b7d  __eqsf2                              
00003b7d  __lesf2                              
00003b7d  __ltsf2                              
00003b7d  __nesf2                              
00003bb9  __muldsi3                            
00003bf5  SYSCFG_DL_init                       
00003c2d  __aeabi_f2iz                         
00003c2d  __fixsfsi                            
00003ccd  Task_IdleFunction                    
00003d01  _IQ24toF                             
00003d61  _IQ24mpy                             
00003db9  __aeabi_i2d                          
00003db9  __floatsidf                          
00003de5  vsprintf                             
00003e11  PID_Init                             
00003edd  SysTick_Increasment                  
00003f05  _c_int00_noargs                      
00003f53  DL_I2C_setClockConfig                
00003f79  __aeabi_ui2d                         
00003f79  __floatunsidf                        
00003f9d  __aeabi_lmul                         
00003f9d  __muldi3                             
00003fc1  PID_SetParams                        
00003fe3  memccpy                              
00004025  Delay                                
00004045  main                                 
00004065  __aeabi_llsl                         
00004065  __ashldi3                            
000041f1  DL_Timer_setCaptCompUpdateMethod     
0000420d  DL_Timer_setClockConfig              
000044f9  DL_Timer_setCaptureCompareOutCtl     
000045af  __TI_zero_init_nomemset              
00004679  __aeabi_uldivmod                     
000046a1  TI_memcpy_small                      
000046b3  __TI_decompress_none                 
000046d5  DL_Timer_setCaptureCompareValue      
000046e5  SYSCFG_DL_SYSTICK_init               
000046f5  wcslen                               
00004705  __aeabi_memset                       
00004705  __aeabi_memset4                      
00004705  __aeabi_memset8                      
00004721  TI_memset_small                      
00004731  Sys_GetTick                          
0000473d  DL_Common_delayCycles                
00004775  SysTick_Handler                      
0000477d  __aeabi_errno_addr                   
00004785  __aeabi_memcpy                       
00004785  __aeabi_memcpy4                      
00004785  __aeabi_memcpy8                      
0000478d  abort                                
00004793  ADC0_IRQHandler                      
00004793  ADC1_IRQHandler                      
00004793  AES_IRQHandler                       
00004793  CANFD0_IRQHandler                    
00004793  DAC0_IRQHandler                      
00004793  DMA_IRQHandler                       
00004793  Default_Handler                      
00004793  GROUP0_IRQHandler                    
00004793  HardFault_Handler                    
00004793  I2C0_IRQHandler                      
00004793  I2C1_IRQHandler                      
00004793  NMI_Handler                          
00004793  PendSV_Handler                       
00004793  RTC_IRQHandler                       
00004793  SPI0_IRQHandler                      
00004793  SPI1_IRQHandler                      
00004793  SVC_Handler                          
00004793  TIMA0_IRQHandler                     
00004793  TIMA1_IRQHandler                     
00004793  TIMG0_IRQHandler                     
00004793  TIMG12_IRQHandler                    
00004793  TIMG6_IRQHandler                     
00004793  TIMG7_IRQHandler                     
00004793  TIMG8_IRQHandler                     
00004793  UART0_IRQHandler                     
00004793  UART1_IRQHandler                     
00004793  UART2_IRQHandler                     
00004793  UART3_IRQHandler                     
00004796  C$$EXIT                              
00004797  HOSTexit                             
0000479b  Reset_Handler                        
000047b1  _system_pre_init                     
000047c0  asc2_1608                            
00004db0  asc2_0806                            
00004fe0  __aeabi_ctype_table_                 
00004fe0  __aeabi_ctype_table_C                
000050e1  _IQ6div_lookup                       
000051e4  __TI_Handler_Table_Base              
000051f0  __TI_Handler_Table_Limit             
000051f8  __TI_CINIT_Base                      
00005208  __TI_CINIT_Limit                     
00005208  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  _sys_memory                          
202004f0  gMotorAFrontBackup                   
20200590  gMotorBFrontBackup                   
20200630  ExISR_Flag                           
20200635  ret                                  
20200638  Motor_Font_Left                      
20200680  Motor_Font_Right                     
202006c8  Data_Tracker_Input                   
202006d0  Motor                                
202006d8  Data_MotorEncoder                    
202006dc  Data_Motor_TarSpeed                  
202006e0  Data_Tracker_Offset                  
202006e4  __aeabi_errno                        
202006e8  delayTick                            
202006ec  uwTick                               
202006f3  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[235 symbols]
