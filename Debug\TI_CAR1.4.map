******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Thu Jul 31 17:46:10 2025

OUTPUT FILE NAME:   <TI_CAR1.4.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00003db9


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00005060  0001afa0  R  X
  SRAM                  20200000   00008000  000008f2  0000770e  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00005060   00005060    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00004550   00004550    r-x .text
  00004610    00004610    000009f0   000009f0    r-- .rodata
  00005000    00005000    00000060   00000060    r-- .cinit
20200000    20200000    000006f4   00000000    rw-
  20200000    20200000    00000400   00000000    rw- .sysmem
  20200400    20200400    00000236   00000000    rw- .bss
  20200638    20200638    000000bc   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00004550     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000220            : _printfi.c.obj (.text._pconv_a)
                  00000cb0    000001dc            : _printfi.c.obj (.text._pconv_g)
                  00000e8c    000001d0     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  0000105c    000001b0     Task.o (.text.Task_Start)
                  0000120c    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000139e    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000013a0    0000015c     Tracker.o (.text.Tracker_Read)
                  000014fc    00000144     PID.o (.text.PID_SProsc)
                  00001640    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  0000177c    00000134            : qsort.c.obj (.text.qsort)
                  000018b0    00000130     OLED.o (.text.OLED_ShowChar)
                  000019e0    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00001b00    00000110     OLED.o (.text.OLED_Init)
                  00001c10    0000010c     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  00001d1c    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001e28    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00001f2c    000000e8     Task_App.o (.text.Task_Motor_PID)
                  00002014    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000020f8    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  000021d4    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  000022ac    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00002384    000000d4     Motor.o (.text.Motor_SetDuty)
                  00002458    000000b4     Task.o (.text.Task_Add)
                  0000250c    000000b0     Interrupt.o (.text.GROUP1_IRQHandler)
                  000025bc    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  0000265e    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00002660    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  000026f8    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorAFront_init)
                  00002784    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorBFront_init)
                  00002810    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  0000289c    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00002920    00000080     Task_App.o (.text.Task_OLED)
                  000029a0    0000007c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00002a1c    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002a98    00000078     Motor.o (.text.Motor_Start)
                  00002b10    00000078     Task_App.o (.text.Task_Init)
                  00002b88    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00002bfc    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00002c00    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00002c74    00000074     SysTick.o (.text.delay_us)
                  00002ce8    0000006e     OLED.o (.text.OLED_ShowString)
                  00002d56    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  00002dc0    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00002e28    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00002e8e    00000002     --HOLE-- [fill = 0]
                  00002e90    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00002ef4    00000064     Key_Led.o (.text.Key_Read)
                  00002f58    00000064     Task_App.o (.text.Task_Key)
                  00002fbc    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  0000301e    00000002     --HOLE-- [fill = 0]
                  00003020    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00003082    00000002     --HOLE-- [fill = 0]
                  00003084    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  000030e4    00000060     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00003144    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  000031a2    00000002     --HOLE-- [fill = 0]
                  000031a4    0000005c     Task_App.o (.text.Task_Tracker)
                  00003200    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  0000325c    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  000032b8    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00003310    00000058            : _printfi.c.obj (.text._pconv_f)
                  00003368    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  000033be    00000002     --HOLE-- [fill = 0]
                  000033c0    00000054     Motor.o (.text.CalculateDutyValue)
                  00003414    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00003468    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  000034ba    00000002     --HOLE-- [fill = 0]
                  000034bc    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  0000350c    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  0000355c    0000004c     OLED.o (.text.OLED_Printf)
                  000035a8    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  000035f2    00000048     Motor.o (.text.Motor_GetSpeed)
                  0000363a    00000002     --HOLE-- [fill = 0]
                  0000363c    00000048     OLED.o (.text.mspm0_i2c_disable)
                  00003684    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000036c8    00000044     Motor.o (.text.SetPWMValue)
                  0000370c    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  0000374e    00000002     --HOLE-- [fill = 0]
                  00003750    00000040     Interrupt.o (.text.Interrupt_Init)
                  00003790    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  000037d0    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00003810    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00003850    0000003e     Task.o (.text.Task_CMP)
                  0000388e    00000002     --HOLE-- [fill = 0]
                  00003890    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000038cc    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00003908    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00003944    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  00003980    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  000039bc    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  000039f8    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00003a34    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00003a6e    00000002     --HOLE-- [fill = 0]
                  00003a70    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00003aaa    00000002     --HOLE-- [fill = 0]
                  00003aac    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00003ae4    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003b18    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003b4c    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00003b80    00000034     Task_App.o (.text.Task_IdleFunction)
                  00003bb4    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  00003be4    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00003c14    0000002c     iqmath.a : _IQNmpy.o (.text._IQ24mpy)
                  00003c40    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00003c6c    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00003c98    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00003cc4    0000002a     PID.o (.text.PID_Init)
                  00003cee    00000028     OLED.o (.text.DL_Common_updateReg)
                  00003d16    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00003d3e    00000002     --HOLE-- [fill = 0]
                  00003d40    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00003d68    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00003d90    00000028     SysTick.o (.text.SysTick_Increasment)
                  00003db8    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00003de0    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00003e06    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00003e2c    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00003e50    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00003e74    00000022     PID.o (.text.PID_SetParams)
                  00003e96    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00003eb8    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00003ed8    00000020     SysTick.o (.text.Delay)
                  00003ef8    00000020     main.o (.text.main)
                  00003f18    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00003f36    00000002     --HOLE-- [fill = 0]
                  00003f38    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00003f54    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00003f70    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00003f8c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00003fa8    0000001c     Interrupt.o (.text.DL_GPIO_enableInterrupt)
                  00003fc4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00003fe0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00003ffc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00004018    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00004034    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  00004050    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  0000406c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00004088    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  000040a4    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000040c0    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000040dc    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  000040f4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  0000410c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00004124    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  0000413c    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  00004154    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  0000416c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00004184    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  0000419c    00000018     OLED.o (.text.DL_GPIO_setPins)
                  000041b4    00000018     Tracker.o (.text.DL_GPIO_setPins)
                  000041cc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  000041e4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  000041fc    00000018     Task_App.o (.text.DL_GPIO_togglePins)
                  00004214    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  0000422c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00004244    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  0000425c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00004274    00000018     OLED.o (.text.DL_I2C_enablePower)
                  0000428c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  000042a4    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  000042bc    00000018     OLED.o (.text.DL_I2C_reset)
                  000042d4    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  000042ec    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00004304    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  0000431c    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00004334    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  0000434c    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00004364    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  0000437c    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00004394    00000018     Motor.o (.text.DL_Timer_startCounter)
                  000043ac    00000018     libc.a : vsprintf.c.obj (.text._outs)
                  000043c4    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  000043da    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  000043f0    00000016     OLED.o (.text.DL_GPIO_readPins)
                  00004406    00000016     Tracker.o (.text.DL_GPIO_readPins)
                  0000441c    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00004432    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  00004446    00000014     Tracker.o (.text.DL_GPIO_clearPins)
                  0000445a    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  0000446e    00000002     --HOLE-- [fill = 0]
                  00004470    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  00004484    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00004498    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  000044ac    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  000044c0    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  000044d4    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  000044e8    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  000044fc    00000012            : memcpy16.S.obj (.text:TI_memcpy_small)
                  0000450e    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00004520    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00004530    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00004540    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00004550    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00004560    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  0000456e    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  0000457c    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  0000458a    00000002     --HOLE-- [fill = 0]
                  0000458c    0000000c     SysTick.o (.text.Sys_GetTick)
                  00004598    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000045a2    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  000045ac    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  000045bc    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  000045c6    0000000a            : vsprintf.c.obj (.text._outc)
                  000045d0    00000008     Interrupt.o (.text.SysTick_Handler)
                  000045d8    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  000045e0    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000045e8    00000006     libc.a : exit.c.obj (.text:abort)
                  000045ee    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000045f2    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000045f6    00000002     --HOLE-- [fill = 0]
                  000045f8    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00004608    00000004            : pre_init.c.obj (.text._system_pre_init)
                  0000460c    00000004     --HOLE-- [fill = 0]

.cinit     0    00005000    00000060     
                  00005000    00000038     (.cinit..data.load) [load image, compression = lzss]
                  00005038    0000000c     (__TI_handler_table)
                  00005044    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  0000504c    00000010     (__TI_cinit_table)
                  0000505c    00000004     --HOLE-- [fill = 0]

.rodata    0    00004610    000009f0     
                  00004610    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  00004c00    00000228     OLED_Font.o (.rodata.asc2_0806)
                  00004e28    00000008     ti_msp_dl_config.o (.rodata.gMotorAFrontConfig)
                  00004e30    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00004f31    00000041     iqmath.a : _IQNtables.o (.rodata._IQ6div_lookup)
                  00004f72    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00004f74    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00004f9c    00000012     Task_App.o (.rodata.str1.16020955549137178199.1)
                  00004fae    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00004fbf    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00004fd0    00000008     ti_msp_dl_config.o (.rodata.gMotorBFrontConfig)
                  00004fd8    00000008     Task_App.o (.rodata.str1.12629676409056169537.1)
                  00004fe0    00000006     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00004fe6    00000005     Task_App.o (.rodata.str1.8896853068034818020.1)
                  00004feb    00000004     Task_App.o (.rodata.str1.10635198597896025474.1)
                  00004fef    00000003     ti_msp_dl_config.o (.rodata.gMotorAFrontClockConfig)
                  00004ff2    00000003     ti_msp_dl_config.o (.rodata.gMotorBFrontClockConfig)
                  00004ff5    0000000b     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.sysmem    0    20200000    00000400     UNINITIALIZED
                  20200000    00000010     libc.a : memory.c.obj (.sysmem)
                  20200010    000003f0     --HOLE--

.bss       0    20200400    00000236     UNINITIALIZED
                  20200400    000000f0     Task.o (.bss.Task_Schedule)
                  202004f0    000000a0     (.common:gMotorAFrontBackup)
                  20200590    000000a0     (.common:gMotorBFrontBackup)
                  20200630    00000004     (.common:ExISR_Flag)
                  20200634    00000001     Task_App.o (.bss.Task_Key.Key_Old)
                  20200635    00000001     (.common:ret)

.data      0    20200638    000000bc     UNINITIALIZED
                  20200638    00000048     Motor.o (.data.Motor_Font_Left)
                  20200680    00000048     Motor.o (.data.Motor_Font_Right)
                  202006c8    00000008     Task_App.o (.data.Data_Tracker_Input)
                  202006d0    00000008     Task_App.o (.data.Motor)
                  202006d8    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202006dc    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  202006e0    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  202006e4    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202006e8    00000004     SysTick.o (.data.delayTick)
                  202006ec    00000004     SysTick.o (.data.uwTick)
                  202006f0    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  202006f2    00000001     Task.o (.data.Task_Num)
                  202006f3    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             2462    64        320    
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         32      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2502    256       320    
                                                               
    .\APP\Src\
       Task_App.o                     748     41        31     
       Interrupt.o                    422     0         5      
    +--+------------------------------+-------+---------+---------+
       Total:                         1170    41        36     
                                                               
    .\BSP\Src\
       OLED_Font.o                    0       2072      0      
       OLED.o                         1858    0         0      
       Task.o                         674     0         241    
       Motor.o                        580     0         144    
       Tracker.o                      414     0         1      
       PID.o                          400     0         0      
       SysTick.o                      200     0         8      
       Key_Led.o                      122     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4248    2072      394    
                                                               
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_timer.o                     356     0         0      
       dl_i2c.o                       132     0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         886     0         0      
                                                               
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/iqmath/lib/ticlang/m0p/rts/mspm0g1x0x_g3x0x/iqmath.a
       _IQNdiv.o                      268     0         0      
       _IQNtables.o                   0       65        0      
       _IQNtoF.o                      48      0         0      
       _IQNmpy.o                      44      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         360     65        0      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       qsort.c.obj                    308     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       vsprintf.c.obj                 78      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5736    291       4      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       fixunsdfsi.S.obj               66      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsidf.S.obj              36      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memset.S.obj             14      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2802    0         0      
                                                               
       Heap:                          0       0         1024   
       Stack:                         0       0         512    
       Linker Generated:              0       92        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   17708   2817      2290   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 0000504c records: 2, size/record: 8, table size: 16
	.data: load addr=00005000, load size=00000038 bytes, run addr=20200638, run size=000000bc bytes, compression=lzss
	.bss: load addr=00005044, load size=00000008 bytes, run addr=20200400, run size=00000236 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00005038 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   0000120d     000045ac     000045aa   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00003db9     000045f8     000045f2   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[2 trampolines]
[2 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00002bfd  ADC0_IRQHandler                      
00002bfd  ADC1_IRQHandler                      
00002bfd  AES_IRQHandler                       
000045ee  C$$EXIT                              
00002bfd  CANFD0_IRQHandler                    
00002bfd  DAC0_IRQHandler                      
00004599  DL_Common_delayCycles                
00003145  DL_I2C_fillControllerTXFIFO          
00003e07  DL_I2C_setClockConfig                
000020f9  DL_SYSCTL_configSYSPLL               
00002e91  DL_SYSCTL_setHFCLKSourceHFXTParams   
00003685  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00001e29  DL_Timer_initFourCCPWMMode           
000040a5  DL_Timer_setCaptCompUpdateMethod     
0000437d  DL_Timer_setCaptureCompareOutCtl     
00004531  DL_Timer_setCaptureCompareValue      
000040c1  DL_Timer_setClockConfig              
00002bfd  DMA_IRQHandler                       
202006d8  Data_MotorEncoder                    
202006dc  Data_Motor_TarSpeed                  
202006c8  Data_Tracker_Input                   
202006e0  Data_Tracker_Offset                  
00002bfd  Default_Handler                      
00003ed9  Delay                                
20200630  ExISR_Flag                           
00002bfd  GROUP0_IRQHandler                    
0000250d  GROUP1_IRQHandler                    
000045ef  HOSTexit                             
00002bfd  HardFault_Handler                    
00002bfd  I2C0_IRQHandler                      
00002bfd  I2C1_IRQHandler                      
00002d57  I2C_OLED_Clear                       
00003945  I2C_OLED_Set_Pos                     
00002661  I2C_OLED_WR_Byte                     
00003085  I2C_OLED_i2c_sda_unlock              
00003751  Interrupt_Init                       
00002ef5  Key_Read                             
202006d0  Motor                                
20200638  Motor_Font_Left                      
20200680  Motor_Font_Right                     
000035f3  Motor_GetSpeed                       
00002385  Motor_SetDuty                        
00002a99  Motor_Start                          
00002bfd  NMI_Handler                          
00001b01  OLED_Init                            
0000355d  OLED_Printf                          
000018b1  OLED_ShowChar                        
00002ce9  OLED_ShowString                      
00003cc5  PID_Init                             
000014fd  PID_SProsc                           
00003e75  PID_SetParams                        
00002bfd  PendSV_Handler                       
00002bfd  RTC_IRQHandler                       
000045f3  Reset_Handler                        
00002bfd  SPI0_IRQHandler                      
00002bfd  SPI1_IRQHandler                      
00002bfd  SVC_Handler                          
00000e8d  SYSCFG_DL_GPIO_init                  
000030e5  SYSCFG_DL_I2C_OLED_init              
000026f9  SYSCFG_DL_MotorAFront_init           
00002785  SYSCFG_DL_MotorBFront_init           
00003415  SYSCFG_DL_SYSCTL_init                
00004541  SYSCFG_DL_SYSTICK_init               
00003b4d  SYSCFG_DL_init                       
000029a1  SYSCFG_DL_initPower                  
000045d1  SysTick_Handler                      
00003d91  SysTick_Increasment                  
0000458d  Sys_GetTick                          
00002bfd  TIMA0_IRQHandler                     
00002bfd  TIMA1_IRQHandler                     
00002bfd  TIMG0_IRQHandler                     
00002bfd  TIMG12_IRQHandler                    
00002bfd  TIMG6_IRQHandler                     
00002bfd  TIMG7_IRQHandler                     
00002bfd  TIMG8_IRQHandler                     
000044fd  TI_memcpy_small                      
0000457d  TI_memset_small                      
00002459  Task_Add                             
00003b81  Task_IdleFunction                    
00002b11  Task_Init                            
00002f59  Task_Key                             
00001f2d  Task_Motor_PID                       
00002921  Task_OLED                            
0000105d  Task_Start                           
000031a5  Task_Tracker                         
000013a1  Tracker_Read                         
00002bfd  UART0_IRQHandler                     
00002bfd  UART1_IRQHandler                     
00002bfd  UART2_IRQHandler                     
00002bfd  UART3_IRQHandler                     
00001c11  _IQ24div                             
00003c15  _IQ24mpy                             
00003bb5  _IQ24toF                             
00004f31  _IQ6div_lookup                       
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000400  __SYSMEM_SIZE                        
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
0000504c  __TI_CINIT_Base                      
0000505c  __TI_CINIT_Limit                     
0000505c  __TI_CINIT_Warm                      
00005038  __TI_Handler_Table_Base              
00005044  __TI_Handler_Table_Limit             
000039f9  __TI_auto_init_nobinit_nopinit       
00002a1d  __TI_decompress_lzss                 
0000450f  __TI_decompress_none                 
000032b9  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
0000441d  __TI_zero_init_nomemset              
00001217  __adddf3                             
000022b7  __addsf3                             
00004e30  __aeabi_ctype_table_                 
00004e30  __aeabi_ctype_table_C                
00002c01  __aeabi_d2f                          
000035a9  __aeabi_d2iz                         
0000370d  __aeabi_d2uiz                        
00001217  __aeabi_dadd                         
00002fbd  __aeabi_dcmpeq                       
00002ff9  __aeabi_dcmpge                       
0000300d  __aeabi_dcmpgt                       
00002fe5  __aeabi_dcmple                       
00002fd1  __aeabi_dcmplt                       
00001d1d  __aeabi_ddiv                         
00002015  __aeabi_dmul                         
0000120d  __aeabi_dsub                         
202006e4  __aeabi_errno                        
000045d9  __aeabi_errno_addr                   
000037d1  __aeabi_f2d                          
00003aad  __aeabi_f2iz                         
000022b7  __aeabi_fadd                         
00003021  __aeabi_fcmpeq                       
0000305d  __aeabi_fcmpge                       
00003071  __aeabi_fcmpgt                       
00003049  __aeabi_fcmple                       
00003035  __aeabi_fcmplt                       
00002811  __aeabi_fmul                         
000022ad  __aeabi_fsub                         
00003c6d  __aeabi_i2d                          
00003981  __aeabi_i2f                          
00003369  __aeabi_idiv                         
0000139f  __aeabi_idiv0                        
00003369  __aeabi_idivmod                      
0000265f  __aeabi_ldiv0                        
00003f19  __aeabi_llsl                         
00003e51  __aeabi_lmul                         
000045e1  __aeabi_memcpy                       
000045e1  __aeabi_memcpy4                      
000045e1  __aeabi_memcpy8                      
00004561  __aeabi_memset                       
00004561  __aeabi_memset4                      
00004561  __aeabi_memset8                      
00003e2d  __aeabi_ui2d                         
00003791  __aeabi_uidiv                        
00003791  __aeabi_uidivmod                     
000044d5  __aeabi_uldivmod                     
00003f19  __ashldi3                            
ffffffff  __binit__                            
00002dc1  __cmpdf2                             
00003a35  __cmpsf2                             
00001d1d  __divdf3                             
00002dc1  __eqdf2                              
00003a35  __eqsf2                              
000037d1  __extendsfdf2                        
000035a9  __fixdfsi                            
00003aad  __fixsfsi                            
0000370d  __fixunsdfsi                         
00003c6d  __floatsidf                          
00003981  __floatsisf                          
00003e2d  __floatunsidf                        
00002b89  __gedf2                              
000039bd  __gesf2                              
00002b89  __gtdf2                              
000039bd  __gtsf2                              
00002dc1  __ledf2                              
00003a35  __lesf2                              
00002dc1  __ltdf2                              
00003a35  __ltsf2                              
UNDEFED   __mpu_init                           
00002015  __muldf3                             
00003e51  __muldi3                             
00003a71  __muldsi3                            
00002811  __mulsf3                             
00002dc1  __nedf2                              
00003a35  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
0000120d  __subdf3                             
000022ad  __subsf3                             
00002c01  __truncdfsf2                         
000025bd  __udivmoddi4                         
00003db9  _c_int00_noargs                      
20200000  _sys_memory                          
UNDEFED   _system_post_cinit                   
00004609  _system_pre_init                     
000045e9  abort                                
00004c00  asc2_0806                            
00004610  asc2_1608                            
00003811  atoi                                 
ffffffff  binit                                
202006e8  delayTick                            
00002c75  delay_us                             
202006f3  enable_group1_irq                    
00003201  frexp                                
00003201  frexpl                               
202004f0  gMotorAFrontBackup                   
20200590  gMotorBFrontBackup                   
00000000  interruptVectors                     
000021d5  ldexp                                
000021d5  ldexpl                               
00003ef9  main                                 
00003e97  memccpy                              
0000177d  qsort                                
20200635  ret                                  
000021d5  scalbn                               
000021d5  scalbnl                              
202006ec  uwTick                               
00003c99  vsprintf                             
00004551  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000400  __SYSMEM_SIZE                        
00000e8d  SYSCFG_DL_GPIO_init                  
0000105d  Task_Start                           
0000120d  __aeabi_dsub                         
0000120d  __subdf3                             
00001217  __adddf3                             
00001217  __aeabi_dadd                         
0000139f  __aeabi_idiv0                        
000013a1  Tracker_Read                         
000014fd  PID_SProsc                           
0000177d  qsort                                
000018b1  OLED_ShowChar                        
00001b01  OLED_Init                            
00001c11  _IQ24div                             
00001d1d  __aeabi_ddiv                         
00001d1d  __divdf3                             
00001e29  DL_Timer_initFourCCPWMMode           
00001f2d  Task_Motor_PID                       
00002015  __aeabi_dmul                         
00002015  __muldf3                             
000020f9  DL_SYSCTL_configSYSPLL               
000021d5  ldexp                                
000021d5  ldexpl                               
000021d5  scalbn                               
000021d5  scalbnl                              
000022ad  __aeabi_fsub                         
000022ad  __subsf3                             
000022b7  __addsf3                             
000022b7  __aeabi_fadd                         
00002385  Motor_SetDuty                        
00002459  Task_Add                             
0000250d  GROUP1_IRQHandler                    
000025bd  __udivmoddi4                         
0000265f  __aeabi_ldiv0                        
00002661  I2C_OLED_WR_Byte                     
000026f9  SYSCFG_DL_MotorAFront_init           
00002785  SYSCFG_DL_MotorBFront_init           
00002811  __aeabi_fmul                         
00002811  __mulsf3                             
00002921  Task_OLED                            
000029a1  SYSCFG_DL_initPower                  
00002a1d  __TI_decompress_lzss                 
00002a99  Motor_Start                          
00002b11  Task_Init                            
00002b89  __gedf2                              
00002b89  __gtdf2                              
00002bfd  ADC0_IRQHandler                      
00002bfd  ADC1_IRQHandler                      
00002bfd  AES_IRQHandler                       
00002bfd  CANFD0_IRQHandler                    
00002bfd  DAC0_IRQHandler                      
00002bfd  DMA_IRQHandler                       
00002bfd  Default_Handler                      
00002bfd  GROUP0_IRQHandler                    
00002bfd  HardFault_Handler                    
00002bfd  I2C0_IRQHandler                      
00002bfd  I2C1_IRQHandler                      
00002bfd  NMI_Handler                          
00002bfd  PendSV_Handler                       
00002bfd  RTC_IRQHandler                       
00002bfd  SPI0_IRQHandler                      
00002bfd  SPI1_IRQHandler                      
00002bfd  SVC_Handler                          
00002bfd  TIMA0_IRQHandler                     
00002bfd  TIMA1_IRQHandler                     
00002bfd  TIMG0_IRQHandler                     
00002bfd  TIMG12_IRQHandler                    
00002bfd  TIMG6_IRQHandler                     
00002bfd  TIMG7_IRQHandler                     
00002bfd  TIMG8_IRQHandler                     
00002bfd  UART0_IRQHandler                     
00002bfd  UART1_IRQHandler                     
00002bfd  UART2_IRQHandler                     
00002bfd  UART3_IRQHandler                     
00002c01  __aeabi_d2f                          
00002c01  __truncdfsf2                         
00002c75  delay_us                             
00002ce9  OLED_ShowString                      
00002d57  I2C_OLED_Clear                       
00002dc1  __cmpdf2                             
00002dc1  __eqdf2                              
00002dc1  __ledf2                              
00002dc1  __ltdf2                              
00002dc1  __nedf2                              
00002e91  DL_SYSCTL_setHFCLKSourceHFXTParams   
00002ef5  Key_Read                             
00002f59  Task_Key                             
00002fbd  __aeabi_dcmpeq                       
00002fd1  __aeabi_dcmplt                       
00002fe5  __aeabi_dcmple                       
00002ff9  __aeabi_dcmpge                       
0000300d  __aeabi_dcmpgt                       
00003021  __aeabi_fcmpeq                       
00003035  __aeabi_fcmplt                       
00003049  __aeabi_fcmple                       
0000305d  __aeabi_fcmpge                       
00003071  __aeabi_fcmpgt                       
00003085  I2C_OLED_i2c_sda_unlock              
000030e5  SYSCFG_DL_I2C_OLED_init              
00003145  DL_I2C_fillControllerTXFIFO          
000031a5  Task_Tracker                         
00003201  frexp                                
00003201  frexpl                               
000032b9  __TI_ltoa                            
00003369  __aeabi_idiv                         
00003369  __aeabi_idivmod                      
00003415  SYSCFG_DL_SYSCTL_init                
0000355d  OLED_Printf                          
000035a9  __aeabi_d2iz                         
000035a9  __fixdfsi                            
000035f3  Motor_GetSpeed                       
00003685  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
0000370d  __aeabi_d2uiz                        
0000370d  __fixunsdfsi                         
00003751  Interrupt_Init                       
00003791  __aeabi_uidiv                        
00003791  __aeabi_uidivmod                     
000037d1  __aeabi_f2d                          
000037d1  __extendsfdf2                        
00003811  atoi                                 
00003945  I2C_OLED_Set_Pos                     
00003981  __aeabi_i2f                          
00003981  __floatsisf                          
000039bd  __gesf2                              
000039bd  __gtsf2                              
000039f9  __TI_auto_init_nobinit_nopinit       
00003a35  __cmpsf2                             
00003a35  __eqsf2                              
00003a35  __lesf2                              
00003a35  __ltsf2                              
00003a35  __nesf2                              
00003a71  __muldsi3                            
00003aad  __aeabi_f2iz                         
00003aad  __fixsfsi                            
00003b4d  SYSCFG_DL_init                       
00003b81  Task_IdleFunction                    
00003bb5  _IQ24toF                             
00003c15  _IQ24mpy                             
00003c6d  __aeabi_i2d                          
00003c6d  __floatsidf                          
00003c99  vsprintf                             
00003cc5  PID_Init                             
00003d91  SysTick_Increasment                  
00003db9  _c_int00_noargs                      
00003e07  DL_I2C_setClockConfig                
00003e2d  __aeabi_ui2d                         
00003e2d  __floatunsidf                        
00003e51  __aeabi_lmul                         
00003e51  __muldi3                             
00003e75  PID_SetParams                        
00003e97  memccpy                              
00003ed9  Delay                                
00003ef9  main                                 
00003f19  __aeabi_llsl                         
00003f19  __ashldi3                            
000040a5  DL_Timer_setCaptCompUpdateMethod     
000040c1  DL_Timer_setClockConfig              
0000437d  DL_Timer_setCaptureCompareOutCtl     
0000441d  __TI_zero_init_nomemset              
000044d5  __aeabi_uldivmod                     
000044fd  TI_memcpy_small                      
0000450f  __TI_decompress_none                 
00004531  DL_Timer_setCaptureCompareValue      
00004541  SYSCFG_DL_SYSTICK_init               
00004551  wcslen                               
00004561  __aeabi_memset                       
00004561  __aeabi_memset4                      
00004561  __aeabi_memset8                      
0000457d  TI_memset_small                      
0000458d  Sys_GetTick                          
00004599  DL_Common_delayCycles                
000045d1  SysTick_Handler                      
000045d9  __aeabi_errno_addr                   
000045e1  __aeabi_memcpy                       
000045e1  __aeabi_memcpy4                      
000045e1  __aeabi_memcpy8                      
000045e9  abort                                
000045ee  C$$EXIT                              
000045ef  HOSTexit                             
000045f3  Reset_Handler                        
00004609  _system_pre_init                     
00004610  asc2_1608                            
00004c00  asc2_0806                            
00004e30  __aeabi_ctype_table_                 
00004e30  __aeabi_ctype_table_C                
00004f31  _IQ6div_lookup                       
00005038  __TI_Handler_Table_Base              
00005044  __TI_Handler_Table_Limit             
0000504c  __TI_CINIT_Base                      
0000505c  __TI_CINIT_Limit                     
0000505c  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  _sys_memory                          
202004f0  gMotorAFrontBackup                   
20200590  gMotorBFrontBackup                   
20200630  ExISR_Flag                           
20200635  ret                                  
20200638  Motor_Font_Left                      
20200680  Motor_Font_Right                     
202006c8  Data_Tracker_Input                   
202006d0  Motor                                
202006d8  Data_MotorEncoder                    
202006dc  Data_Motor_TarSpeed                  
202006e0  Data_Tracker_Offset                  
202006e4  __aeabi_errno                        
202006e8  delayTick                            
202006ec  uwTick                               
202006f3  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[233 symbols]
