******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Thu Jul 31 18:17:07 2025

OUTPUT FILE NAME:   <TI_CAR1.4.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00003e45


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00005100  0001af00  R  X
  SRAM                  20200000   00008000  000008f0  00007710  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00005100   00005100    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000045f0   000045f0    r-x .text
  000046b0    000046b0    000009f0   000009f0    r-- .rodata
  000050a0    000050a0    00000060   00000060    r-- .cinit
20200000    20200000    000006f2   00000000    rw-
  20200000    20200000    00000400   00000000    rw- .sysmem
  20200400    20200400    00000236   00000000    rw- .bss
  20200638    20200638    000000ba   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000045f0     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000220            : _printfi.c.obj (.text._pconv_a)
                  00000cb0    000001dc            : _printfi.c.obj (.text._pconv_g)
                  00000e8c    000001bc     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001048    000001b0     Task.o (.text.Task_Start)
                  000011f8    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000138a    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  0000138c    0000015c     Tracker.o (.text.Tracker_Read)
                  000014e8    00000144     PID.o (.text.PID_SProsc)
                  0000162c    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00001768    00000134            : qsort.c.obj (.text.qsort)
                  0000189c    00000130     OLED.o (.text.OLED_ShowChar)
                  000019cc    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00001aec    00000110     OLED.o (.text.OLED_Init)
                  00001bfc    0000010c     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  00001d08    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001e14    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00001f18    00000104     Task_App.o (.text.Task_Motor_PID)
                  0000201c    000000e8     Task_App.o (.text.Task_Key)
                  00002104    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000021e8    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  000022c4    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  0000239c    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00002474    000000d4     Motor.o (.text.Motor_SetDuty)
                  00002548    000000b4     Task.o (.text.Task_Add)
                  000025fc    000000b0     Interrupt.o (.text.GROUP1_IRQHandler)
                  000026ac    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  0000274e    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00002750    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  000027e8    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorAFront_init)
                  00002874    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorBFront_init)
                  00002900    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  0000298c    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00002a10    00000080     Task_App.o (.text.Task_OLED)
                  00002a90    0000007c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00002b0c    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002b88    00000078     Motor.o (.text.Motor_Start)
                  00002c00    00000078     Task_App.o (.text.Task_Init)
                  00002c78    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00002cec    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00002cf0    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00002d64    00000074     SysTick.o (.text.delay_us)
                  00002dd8    0000006e     OLED.o (.text.OLED_ShowString)
                  00002e46    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  00002eb0    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00002f18    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00002f7e    00000002     --HOLE-- [fill = 0]
                  00002f80    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00002fe4    00000064     Key_Led.o (.text.Key_Read)
                  00003048    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  000030aa    00000002     --HOLE-- [fill = 0]
                  000030ac    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  0000310e    00000002     --HOLE-- [fill = 0]
                  00003110    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00003170    00000060     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  000031d0    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  0000322e    00000002     --HOLE-- [fill = 0]
                  00003230    0000005c     Task_App.o (.text.Task_Tracker)
                  0000328c    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  000032e8    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  00003344    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  0000339c    00000058            : _printfi.c.obj (.text._pconv_f)
                  000033f4    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  0000344a    00000002     --HOLE-- [fill = 0]
                  0000344c    00000054     Motor.o (.text.CalculateDutyValue)
                  000034a0    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000034f4    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00003546    00000002     --HOLE-- [fill = 0]
                  00003548    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  00003598    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  000035e8    0000004c     OLED.o (.text.OLED_Printf)
                  00003634    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  0000367e    00000048     Motor.o (.text.Motor_GetSpeed)
                  000036c6    00000002     --HOLE-- [fill = 0]
                  000036c8    00000048     OLED.o (.text.mspm0_i2c_disable)
                  00003710    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00003754    00000044     Motor.o (.text.SetPWMValue)
                  00003798    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  000037da    00000002     --HOLE-- [fill = 0]
                  000037dc    00000040     Interrupt.o (.text.Interrupt_Init)
                  0000381c    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  0000385c    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  0000389c    00000040     libc.a : atoi.c.obj (.text.atoi)
                  000038dc    0000003e     Task.o (.text.Task_CMP)
                  0000391a    00000002     --HOLE-- [fill = 0]
                  0000391c    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00003958    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00003994    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  000039d0    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  00003a0c    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00003a48    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00003a84    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00003ac0    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00003afa    00000002     --HOLE-- [fill = 0]
                  00003afc    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00003b36    00000002     --HOLE-- [fill = 0]
                  00003b38    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00003b70    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003ba4    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003bd8    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00003c0c    00000034     Task_App.o (.text.Task_IdleFunction)
                  00003c40    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  00003c70    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00003ca0    0000002c     iqmath.a : _IQNmpy.o (.text._IQ24mpy)
                  00003ccc    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00003cf8    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00003d24    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00003d50    0000002a     PID.o (.text.PID_Init)
                  00003d7a    00000028     OLED.o (.text.DL_Common_updateReg)
                  00003da2    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00003dca    00000002     --HOLE-- [fill = 0]
                  00003dcc    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00003df4    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00003e1c    00000028     SysTick.o (.text.SysTick_Increasment)
                  00003e44    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00003e6c    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00003e92    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00003eb8    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00003edc    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00003f00    00000022     PID.o (.text.PID_SetParams)
                  00003f22    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00003f44    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00003f64    00000020     SysTick.o (.text.Delay)
                  00003f84    00000020     main.o (.text.main)
                  00003fa4    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00003fc2    00000002     --HOLE-- [fill = 0]
                  00003fc4    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00003fe0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00003ffc    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00004018    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00004034    0000001c     Interrupt.o (.text.DL_GPIO_enableInterrupt)
                  00004050    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  0000406c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00004088    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  000040a4    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  000040c0    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  000040dc    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  000040f8    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00004114    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00004130    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  0000414c    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00004168    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  00004180    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00004198    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  000041b0    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  000041c8    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  000041e0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  000041f8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00004210    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00004228    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00004240    00000018     Task_App.o (.text.DL_GPIO_setPins)
                  00004258    00000018     Tracker.o (.text.DL_GPIO_setPins)
                  00004270    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00004288    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  000042a0    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  000042b8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  000042d0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  000042e8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00004300    00000018     OLED.o (.text.DL_I2C_enablePower)
                  00004318    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00004330    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  00004348    00000018     OLED.o (.text.DL_I2C_reset)
                  00004360    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00004378    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00004390    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  000043a8    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  000043c0    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000043d8    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000043f0    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00004408    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00004420    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00004438    00000018     libc.a : vsprintf.c.obj (.text._outs)
                  00004450    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00004466    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  0000447c    00000016     OLED.o (.text.DL_GPIO_readPins)
                  00004492    00000016     Tracker.o (.text.DL_GPIO_readPins)
                  000044a8    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000044be    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  000044d2    00000014     Task_App.o (.text.DL_GPIO_clearPins)
                  000044e6    00000014     Tracker.o (.text.DL_GPIO_clearPins)
                  000044fa    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  0000450e    00000002     --HOLE-- [fill = 0]
                  00004510    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  00004524    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00004538    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  0000454c    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00004560    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00004574    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00004588    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  0000459c    00000012            : memcpy16.S.obj (.text:TI_memcpy_small)
                  000045ae    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000045c0    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  000045d0    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  000045e0    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  000045f0    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00004600    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  0000460e    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  0000461c    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  0000462a    00000002     --HOLE-- [fill = 0]
                  0000462c    0000000c     SysTick.o (.text.Sys_GetTick)
                  00004638    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00004642    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  0000464c    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  0000465c    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00004666    0000000a            : vsprintf.c.obj (.text._outc)
                  00004670    00000008     Interrupt.o (.text.SysTick_Handler)
                  00004678    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00004680    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00004688    00000006     libc.a : exit.c.obj (.text:abort)
                  0000468e    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00004692    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00004696    00000002     --HOLE-- [fill = 0]
                  00004698    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  000046a8    00000004            : pre_init.c.obj (.text._system_pre_init)
                  000046ac    00000004     --HOLE-- [fill = 0]

.cinit     0    000050a0    00000060     
                  000050a0    00000036     (.cinit..data.load) [load image, compression = lzss]
                  000050d6    00000002     --HOLE-- [fill = 0]
                  000050d8    0000000c     (__TI_handler_table)
                  000050e4    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000050ec    00000010     (__TI_cinit_table)
                  000050fc    00000004     --HOLE-- [fill = 0]

.rodata    0    000046b0    000009f0     
                  000046b0    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  00004ca0    00000228     OLED_Font.o (.rodata.asc2_0806)
                  00004ec8    00000008     ti_msp_dl_config.o (.rodata.gMotorAFrontConfig)
                  00004ed0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00004fd1    00000041     iqmath.a : _IQNtables.o (.rodata._IQ6div_lookup)
                  00005012    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00005014    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  0000503c    00000012     Task_App.o (.rodata.str1.16020955549137178199.1)
                  0000504e    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  0000505f    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00005070    00000008     ti_msp_dl_config.o (.rodata.gMotorBFrontConfig)
                  00005078    00000008     Task_App.o (.rodata.str1.12629676409056169537.1)
                  00005080    00000006     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00005086    00000005     Task_App.o (.rodata.str1.8896853068034818020.1)
                  0000508b    00000004     Task_App.o (.rodata.str1.10635198597896025474.1)
                  0000508f    00000003     ti_msp_dl_config.o (.rodata.gMotorAFrontClockConfig)
                  00005092    00000003     ti_msp_dl_config.o (.rodata.gMotorBFrontClockConfig)
                  00005095    0000000b     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.sysmem    0    20200000    00000400     UNINITIALIZED
                  20200000    00000010     libc.a : memory.c.obj (.sysmem)
                  20200010    000003f0     --HOLE--

.bss       0    20200400    00000236     UNINITIALIZED
                  20200400    000000f0     Task.o (.bss.Task_Schedule)
                  202004f0    000000a0     (.common:gMotorAFrontBackup)
                  20200590    000000a0     (.common:gMotorBFrontBackup)
                  20200630    00000004     (.common:ExISR_Flag)
                  20200634    00000001     Task_App.o (.bss.Task_Key.Key_Old)
                  20200635    00000001     (.common:ret)

.data      0    20200638    000000ba     UNINITIALIZED
                  20200638    00000048     Motor.o (.data.Motor_Font_Left)
                  20200680    00000048     Motor.o (.data.Motor_Font_Right)
                  202006c8    00000008     Task_App.o (.data.Data_Tracker_Input)
                  202006d0    00000008     Task_App.o (.data.Motor)
                  202006d8    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202006dc    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  202006e0    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202006e4    00000004     SysTick.o (.data.delayTick)
                  202006e8    00000004     SysTick.o (.data.uwTick)
                  202006ec    00000002     Task_App.o (.data.Data_Motor_TarSpeed)
                  202006ee    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  202006f0    00000001     Task.o (.data.Task_Num)
                  202006f1    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             2442    64        320    
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         32      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2482    256       320    
                                                               
    .\APP\Src\
       Task_App.o                     928     41        29     
       Interrupt.o                    422     0         5      
    +--+------------------------------+-------+---------+---------+
       Total:                         1350    41        34     
                                                               
    .\BSP\Src\
       OLED_Font.o                    0       2072      0      
       OLED.o                         1858    0         0      
       Task.o                         674     0         241    
       Motor.o                        580     0         144    
       Tracker.o                      414     0         1      
       PID.o                          400     0         0      
       SysTick.o                      200     0         8      
       Key_Led.o                      122     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4248    2072      394    
                                                               
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_timer.o                     356     0         0      
       dl_i2c.o                       132     0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         886     0         0      
                                                               
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/iqmath/lib/ticlang/m0p/rts/mspm0g1x0x_g3x0x/iqmath.a
       _IQNdiv.o                      268     0         0      
       _IQNtables.o                   0       65        0      
       _IQNtoF.o                      48      0         0      
       _IQNmpy.o                      44      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         360     65        0      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       qsort.c.obj                    308     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       vsprintf.c.obj                 78      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5736    291       4      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       fixunsdfsi.S.obj               66      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsidf.S.obj              36      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memset.S.obj             14      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2802    0         0      
                                                               
       Heap:                          0       0         1024   
       Stack:                         0       0         512    
       Linker Generated:              0       90        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   17868   2815      2288   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000050ec records: 2, size/record: 8, table size: 16
	.data: load addr=000050a0, load size=00000036 bytes, run addr=20200638, run size=000000ba bytes, compression=lzss
	.bss: load addr=000050e4, load size=00000008 bytes, run addr=20200400, run size=00000236 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000050d8 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   000011f9     0000464c     0000464a   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00003e45     00004698     00004692   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[2 trampolines]
[2 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00002ced  ADC0_IRQHandler                      
00002ced  ADC1_IRQHandler                      
00002ced  AES_IRQHandler                       
0000468e  C$$EXIT                              
00002ced  CANFD0_IRQHandler                    
00002ced  DAC0_IRQHandler                      
00004639  DL_Common_delayCycles                
000031d1  DL_I2C_fillControllerTXFIFO          
00003e93  DL_I2C_setClockConfig                
000021e9  DL_SYSCTL_configSYSPLL               
00002f81  DL_SYSCTL_setHFCLKSourceHFXTParams   
00003711  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00001e15  DL_Timer_initFourCCPWMMode           
00004131  DL_Timer_setCaptCompUpdateMethod     
00004409  DL_Timer_setCaptureCompareOutCtl     
000045d1  DL_Timer_setCaptureCompareValue      
0000414d  DL_Timer_setClockConfig              
00002ced  DMA_IRQHandler                       
202006d8  Data_MotorEncoder                    
202006ec  Data_Motor_TarSpeed                  
202006c8  Data_Tracker_Input                   
202006dc  Data_Tracker_Offset                  
00002ced  Default_Handler                      
00003f65  Delay                                
20200630  ExISR_Flag                           
00002ced  GROUP0_IRQHandler                    
000025fd  GROUP1_IRQHandler                    
0000468f  HOSTexit                             
00002ced  HardFault_Handler                    
00002ced  I2C0_IRQHandler                      
00002ced  I2C1_IRQHandler                      
00002e47  I2C_OLED_Clear                       
000039d1  I2C_OLED_Set_Pos                     
00002751  I2C_OLED_WR_Byte                     
00003111  I2C_OLED_i2c_sda_unlock              
000037dd  Interrupt_Init                       
00002fe5  Key_Read                             
202006d0  Motor                                
20200638  Motor_Font_Left                      
20200680  Motor_Font_Right                     
0000367f  Motor_GetSpeed                       
00002475  Motor_SetDuty                        
00002b89  Motor_Start                          
00002ced  NMI_Handler                          
00001aed  OLED_Init                            
000035e9  OLED_Printf                          
0000189d  OLED_ShowChar                        
00002dd9  OLED_ShowString                      
00003d51  PID_Init                             
000014e9  PID_SProsc                           
00003f01  PID_SetParams                        
00002ced  PendSV_Handler                       
00002ced  RTC_IRQHandler                       
00004693  Reset_Handler                        
00002ced  SPI0_IRQHandler                      
00002ced  SPI1_IRQHandler                      
00002ced  SVC_Handler                          
00000e8d  SYSCFG_DL_GPIO_init                  
00003171  SYSCFG_DL_I2C_OLED_init              
000027e9  SYSCFG_DL_MotorAFront_init           
00002875  SYSCFG_DL_MotorBFront_init           
000034a1  SYSCFG_DL_SYSCTL_init                
000045e1  SYSCFG_DL_SYSTICK_init               
00003bd9  SYSCFG_DL_init                       
00002a91  SYSCFG_DL_initPower                  
00004671  SysTick_Handler                      
00003e1d  SysTick_Increasment                  
0000462d  Sys_GetTick                          
00002ced  TIMA0_IRQHandler                     
00002ced  TIMA1_IRQHandler                     
00002ced  TIMG0_IRQHandler                     
00002ced  TIMG12_IRQHandler                    
00002ced  TIMG6_IRQHandler                     
00002ced  TIMG7_IRQHandler                     
00002ced  TIMG8_IRQHandler                     
0000459d  TI_memcpy_small                      
0000461d  TI_memset_small                      
00002549  Task_Add                             
00003c0d  Task_IdleFunction                    
00002c01  Task_Init                            
0000201d  Task_Key                             
00001f19  Task_Motor_PID                       
00002a11  Task_OLED                            
00001049  Task_Start                           
00003231  Task_Tracker                         
0000138d  Tracker_Read                         
00002ced  UART0_IRQHandler                     
00002ced  UART1_IRQHandler                     
00002ced  UART2_IRQHandler                     
00002ced  UART3_IRQHandler                     
00001bfd  _IQ24div                             
00003ca1  _IQ24mpy                             
00003c41  _IQ24toF                             
00004fd1  _IQ6div_lookup                       
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000400  __SYSMEM_SIZE                        
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
000050ec  __TI_CINIT_Base                      
000050fc  __TI_CINIT_Limit                     
000050fc  __TI_CINIT_Warm                      
000050d8  __TI_Handler_Table_Base              
000050e4  __TI_Handler_Table_Limit             
00003a85  __TI_auto_init_nobinit_nopinit       
00002b0d  __TI_decompress_lzss                 
000045af  __TI_decompress_none                 
00003345  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
000044a9  __TI_zero_init_nomemset              
00001203  __adddf3                             
000023a7  __addsf3                             
00004ed0  __aeabi_ctype_table_                 
00004ed0  __aeabi_ctype_table_C                
00002cf1  __aeabi_d2f                          
00003635  __aeabi_d2iz                         
00003799  __aeabi_d2uiz                        
00001203  __aeabi_dadd                         
00003049  __aeabi_dcmpeq                       
00003085  __aeabi_dcmpge                       
00003099  __aeabi_dcmpgt                       
00003071  __aeabi_dcmple                       
0000305d  __aeabi_dcmplt                       
00001d09  __aeabi_ddiv                         
00002105  __aeabi_dmul                         
000011f9  __aeabi_dsub                         
202006e0  __aeabi_errno                        
00004679  __aeabi_errno_addr                   
0000385d  __aeabi_f2d                          
00003b39  __aeabi_f2iz                         
000023a7  __aeabi_fadd                         
000030ad  __aeabi_fcmpeq                       
000030e9  __aeabi_fcmpge                       
000030fd  __aeabi_fcmpgt                       
000030d5  __aeabi_fcmple                       
000030c1  __aeabi_fcmplt                       
00002901  __aeabi_fmul                         
0000239d  __aeabi_fsub                         
00003cf9  __aeabi_i2d                          
00003a0d  __aeabi_i2f                          
000033f5  __aeabi_idiv                         
0000138b  __aeabi_idiv0                        
000033f5  __aeabi_idivmod                      
0000274f  __aeabi_ldiv0                        
00003fa5  __aeabi_llsl                         
00003edd  __aeabi_lmul                         
00004681  __aeabi_memcpy                       
00004681  __aeabi_memcpy4                      
00004681  __aeabi_memcpy8                      
00004601  __aeabi_memset                       
00004601  __aeabi_memset4                      
00004601  __aeabi_memset8                      
00003eb9  __aeabi_ui2d                         
0000381d  __aeabi_uidiv                        
0000381d  __aeabi_uidivmod                     
00004575  __aeabi_uldivmod                     
00003fa5  __ashldi3                            
ffffffff  __binit__                            
00002eb1  __cmpdf2                             
00003ac1  __cmpsf2                             
00001d09  __divdf3                             
00002eb1  __eqdf2                              
00003ac1  __eqsf2                              
0000385d  __extendsfdf2                        
00003635  __fixdfsi                            
00003b39  __fixsfsi                            
00003799  __fixunsdfsi                         
00003cf9  __floatsidf                          
00003a0d  __floatsisf                          
00003eb9  __floatunsidf                        
00002c79  __gedf2                              
00003a49  __gesf2                              
00002c79  __gtdf2                              
00003a49  __gtsf2                              
00002eb1  __ledf2                              
00003ac1  __lesf2                              
00002eb1  __ltdf2                              
00003ac1  __ltsf2                              
UNDEFED   __mpu_init                           
00002105  __muldf3                             
00003edd  __muldi3                             
00003afd  __muldsi3                            
00002901  __mulsf3                             
00002eb1  __nedf2                              
00003ac1  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000011f9  __subdf3                             
0000239d  __subsf3                             
00002cf1  __truncdfsf2                         
000026ad  __udivmoddi4                         
00003e45  _c_int00_noargs                      
20200000  _sys_memory                          
UNDEFED   _system_post_cinit                   
000046a9  _system_pre_init                     
00004689  abort                                
00004ca0  asc2_0806                            
000046b0  asc2_1608                            
0000389d  atoi                                 
ffffffff  binit                                
202006e4  delayTick                            
00002d65  delay_us                             
202006f1  enable_group1_irq                    
0000328d  frexp                                
0000328d  frexpl                               
202004f0  gMotorAFrontBackup                   
20200590  gMotorBFrontBackup                   
00000000  interruptVectors                     
000022c5  ldexp                                
000022c5  ldexpl                               
00003f85  main                                 
00003f23  memccpy                              
00001769  qsort                                
20200635  ret                                  
000022c5  scalbn                               
000022c5  scalbnl                              
202006e8  uwTick                               
00003d25  vsprintf                             
000045f1  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000400  __SYSMEM_SIZE                        
00000e8d  SYSCFG_DL_GPIO_init                  
00001049  Task_Start                           
000011f9  __aeabi_dsub                         
000011f9  __subdf3                             
00001203  __adddf3                             
00001203  __aeabi_dadd                         
0000138b  __aeabi_idiv0                        
0000138d  Tracker_Read                         
000014e9  PID_SProsc                           
00001769  qsort                                
0000189d  OLED_ShowChar                        
00001aed  OLED_Init                            
00001bfd  _IQ24div                             
00001d09  __aeabi_ddiv                         
00001d09  __divdf3                             
00001e15  DL_Timer_initFourCCPWMMode           
00001f19  Task_Motor_PID                       
0000201d  Task_Key                             
00002105  __aeabi_dmul                         
00002105  __muldf3                             
000021e9  DL_SYSCTL_configSYSPLL               
000022c5  ldexp                                
000022c5  ldexpl                               
000022c5  scalbn                               
000022c5  scalbnl                              
0000239d  __aeabi_fsub                         
0000239d  __subsf3                             
000023a7  __addsf3                             
000023a7  __aeabi_fadd                         
00002475  Motor_SetDuty                        
00002549  Task_Add                             
000025fd  GROUP1_IRQHandler                    
000026ad  __udivmoddi4                         
0000274f  __aeabi_ldiv0                        
00002751  I2C_OLED_WR_Byte                     
000027e9  SYSCFG_DL_MotorAFront_init           
00002875  SYSCFG_DL_MotorBFront_init           
00002901  __aeabi_fmul                         
00002901  __mulsf3                             
00002a11  Task_OLED                            
00002a91  SYSCFG_DL_initPower                  
00002b0d  __TI_decompress_lzss                 
00002b89  Motor_Start                          
00002c01  Task_Init                            
00002c79  __gedf2                              
00002c79  __gtdf2                              
00002ced  ADC0_IRQHandler                      
00002ced  ADC1_IRQHandler                      
00002ced  AES_IRQHandler                       
00002ced  CANFD0_IRQHandler                    
00002ced  DAC0_IRQHandler                      
00002ced  DMA_IRQHandler                       
00002ced  Default_Handler                      
00002ced  GROUP0_IRQHandler                    
00002ced  HardFault_Handler                    
00002ced  I2C0_IRQHandler                      
00002ced  I2C1_IRQHandler                      
00002ced  NMI_Handler                          
00002ced  PendSV_Handler                       
00002ced  RTC_IRQHandler                       
00002ced  SPI0_IRQHandler                      
00002ced  SPI1_IRQHandler                      
00002ced  SVC_Handler                          
00002ced  TIMA0_IRQHandler                     
00002ced  TIMA1_IRQHandler                     
00002ced  TIMG0_IRQHandler                     
00002ced  TIMG12_IRQHandler                    
00002ced  TIMG6_IRQHandler                     
00002ced  TIMG7_IRQHandler                     
00002ced  TIMG8_IRQHandler                     
00002ced  UART0_IRQHandler                     
00002ced  UART1_IRQHandler                     
00002ced  UART2_IRQHandler                     
00002ced  UART3_IRQHandler                     
00002cf1  __aeabi_d2f                          
00002cf1  __truncdfsf2                         
00002d65  delay_us                             
00002dd9  OLED_ShowString                      
00002e47  I2C_OLED_Clear                       
00002eb1  __cmpdf2                             
00002eb1  __eqdf2                              
00002eb1  __ledf2                              
00002eb1  __ltdf2                              
00002eb1  __nedf2                              
00002f81  DL_SYSCTL_setHFCLKSourceHFXTParams   
00002fe5  Key_Read                             
00003049  __aeabi_dcmpeq                       
0000305d  __aeabi_dcmplt                       
00003071  __aeabi_dcmple                       
00003085  __aeabi_dcmpge                       
00003099  __aeabi_dcmpgt                       
000030ad  __aeabi_fcmpeq                       
000030c1  __aeabi_fcmplt                       
000030d5  __aeabi_fcmple                       
000030e9  __aeabi_fcmpge                       
000030fd  __aeabi_fcmpgt                       
00003111  I2C_OLED_i2c_sda_unlock              
00003171  SYSCFG_DL_I2C_OLED_init              
000031d1  DL_I2C_fillControllerTXFIFO          
00003231  Task_Tracker                         
0000328d  frexp                                
0000328d  frexpl                               
00003345  __TI_ltoa                            
000033f5  __aeabi_idiv                         
000033f5  __aeabi_idivmod                      
000034a1  SYSCFG_DL_SYSCTL_init                
000035e9  OLED_Printf                          
00003635  __aeabi_d2iz                         
00003635  __fixdfsi                            
0000367f  Motor_GetSpeed                       
00003711  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00003799  __aeabi_d2uiz                        
00003799  __fixunsdfsi                         
000037dd  Interrupt_Init                       
0000381d  __aeabi_uidiv                        
0000381d  __aeabi_uidivmod                     
0000385d  __aeabi_f2d                          
0000385d  __extendsfdf2                        
0000389d  atoi                                 
000039d1  I2C_OLED_Set_Pos                     
00003a0d  __aeabi_i2f                          
00003a0d  __floatsisf                          
00003a49  __gesf2                              
00003a49  __gtsf2                              
00003a85  __TI_auto_init_nobinit_nopinit       
00003ac1  __cmpsf2                             
00003ac1  __eqsf2                              
00003ac1  __lesf2                              
00003ac1  __ltsf2                              
00003ac1  __nesf2                              
00003afd  __muldsi3                            
00003b39  __aeabi_f2iz                         
00003b39  __fixsfsi                            
00003bd9  SYSCFG_DL_init                       
00003c0d  Task_IdleFunction                    
00003c41  _IQ24toF                             
00003ca1  _IQ24mpy                             
00003cf9  __aeabi_i2d                          
00003cf9  __floatsidf                          
00003d25  vsprintf                             
00003d51  PID_Init                             
00003e1d  SysTick_Increasment                  
00003e45  _c_int00_noargs                      
00003e93  DL_I2C_setClockConfig                
00003eb9  __aeabi_ui2d                         
00003eb9  __floatunsidf                        
00003edd  __aeabi_lmul                         
00003edd  __muldi3                             
00003f01  PID_SetParams                        
00003f23  memccpy                              
00003f65  Delay                                
00003f85  main                                 
00003fa5  __aeabi_llsl                         
00003fa5  __ashldi3                            
00004131  DL_Timer_setCaptCompUpdateMethod     
0000414d  DL_Timer_setClockConfig              
00004409  DL_Timer_setCaptureCompareOutCtl     
000044a9  __TI_zero_init_nomemset              
00004575  __aeabi_uldivmod                     
0000459d  TI_memcpy_small                      
000045af  __TI_decompress_none                 
000045d1  DL_Timer_setCaptureCompareValue      
000045e1  SYSCFG_DL_SYSTICK_init               
000045f1  wcslen                               
00004601  __aeabi_memset                       
00004601  __aeabi_memset4                      
00004601  __aeabi_memset8                      
0000461d  TI_memset_small                      
0000462d  Sys_GetTick                          
00004639  DL_Common_delayCycles                
00004671  SysTick_Handler                      
00004679  __aeabi_errno_addr                   
00004681  __aeabi_memcpy                       
00004681  __aeabi_memcpy4                      
00004681  __aeabi_memcpy8                      
00004689  abort                                
0000468e  C$$EXIT                              
0000468f  HOSTexit                             
00004693  Reset_Handler                        
000046a9  _system_pre_init                     
000046b0  asc2_1608                            
00004ca0  asc2_0806                            
00004ed0  __aeabi_ctype_table_                 
00004ed0  __aeabi_ctype_table_C                
00004fd1  _IQ6div_lookup                       
000050d8  __TI_Handler_Table_Base              
000050e4  __TI_Handler_Table_Limit             
000050ec  __TI_CINIT_Base                      
000050fc  __TI_CINIT_Limit                     
000050fc  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  _sys_memory                          
202004f0  gMotorAFrontBackup                   
20200590  gMotorBFrontBackup                   
20200630  ExISR_Flag                           
20200635  ret                                  
20200638  Motor_Font_Left                      
20200680  Motor_Font_Right                     
202006c8  Data_Tracker_Input                   
202006d0  Motor                                
202006d8  Data_MotorEncoder                    
202006dc  Data_Tracker_Offset                  
202006e0  __aeabi_errno                        
202006e4  delayTick                            
202006e8  uwTick                               
202006ec  Data_Motor_TarSpeed                  
202006f1  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[233 symbols]
