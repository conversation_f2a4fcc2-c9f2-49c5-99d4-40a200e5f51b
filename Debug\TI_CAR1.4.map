******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Thu Jul 31 18:45:15 2025

OUTPUT FILE NAME:   <TI_CAR1.4.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00003e25


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000050e0  0001af20  R  X
  SRAM                  20200000   00008000  000008f2  0000770e  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000050e0   000050e0    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000045d0   000045d0    r-x .text
  00004690    00004690    000009f0   000009f0    r-- .rodata
  00005080    00005080    00000060   00000060    r-- .cinit
20200000    20200000    000006f4   00000000    rw-
  20200000    20200000    00000400   00000000    rw- .sysmem
  20200400    20200400    00000236   00000000    rw- .bss
  20200638    20200638    000000bc   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000045d0     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000220            : _printfi.c.obj (.text._pconv_a)
                  00000cb0    000001dc            : _printfi.c.obj (.text._pconv_g)
                  00000e8c    000001bc     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001048    000001b0     Task.o (.text.Task_Start)
                  000011f8    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000138a    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  0000138c    0000015c     Tracker.o (.text.Tracker_Read)
                  000014e8    00000144     PID.o (.text.PID_SProsc)
                  0000162c    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00001768    00000134            : qsort.c.obj (.text.qsort)
                  0000189c    00000130     OLED.o (.text.OLED_ShowChar)
                  000019cc    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00001aec    00000110     OLED.o (.text.OLED_Init)
                  00001bfc    0000010c     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  00001d08    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001e14    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00001f18    000000e8     Task_App.o (.text.Task_Key)
                  00002000    000000e8     Task_App.o (.text.Task_Motor_PID)
                  000020e8    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000021cc    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  000022a8    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00002380    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00002458    000000d4     Motor.o (.text.Motor_SetDuty)
                  0000252c    000000b4     Task.o (.text.Task_Add)
                  000025e0    000000b0     Interrupt.o (.text.GROUP1_IRQHandler)
                  00002690    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00002732    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00002734    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  000027cc    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorAFront_init)
                  00002858    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorBFront_init)
                  000028e4    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00002970    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  000029f4    00000080     Task_App.o (.text.Task_OLED)
                  00002a74    0000007c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00002af0    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002b6c    00000078     Motor.o (.text.Motor_Start)
                  00002be4    00000078     Task_App.o (.text.Task_Init)
                  00002c5c    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00002cd0    00000074                            : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00002d44    00000074     SysTick.o (.text.delay_us)
                  00002db8    0000006e     OLED.o (.text.OLED_ShowString)
                  00002e26    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  00002e90    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00002ef8    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00002f5e    00000002     --HOLE-- [fill = 0]
                  00002f60    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00002fc4    00000064     Key_Led.o (.text.Key_Read)
                  00003028    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  0000308a    00000002     --HOLE-- [fill = 0]
                  0000308c    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  000030ee    00000002     --HOLE-- [fill = 0]
                  000030f0    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00003150    00000060     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  000031b0    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  0000320e    00000002     --HOLE-- [fill = 0]
                  00003210    0000005c     Task_App.o (.text.Task_Tracker)
                  0000326c    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  000032c8    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  00003324    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  0000337c    00000058            : _printfi.c.obj (.text._pconv_f)
                  000033d4    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  0000342a    00000002     --HOLE-- [fill = 0]
                  0000342c    00000054     Motor.o (.text.CalculateDutyValue)
                  00003480    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000034d4    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00003526    00000002     --HOLE-- [fill = 0]
                  00003528    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  00003578    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  000035c8    0000004c     OLED.o (.text.OLED_Printf)
                  00003614    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  0000365e    00000048     Motor.o (.text.Motor_GetSpeed)
                  000036a6    00000002     --HOLE-- [fill = 0]
                  000036a8    00000048     OLED.o (.text.mspm0_i2c_disable)
                  000036f0    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00003734    00000044     Motor.o (.text.SetPWMValue)
                  00003778    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  000037ba    00000002     --HOLE-- [fill = 0]
                  000037bc    00000040     Interrupt.o (.text.Interrupt_Init)
                  000037fc    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  0000383c    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  0000387c    00000040     libc.a : atoi.c.obj (.text.atoi)
                  000038bc    0000003e     Task.o (.text.Task_CMP)
                  000038fa    00000002     --HOLE-- [fill = 0]
                  000038fc    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00003938    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00003974    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  000039b0    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  000039ec    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00003a28    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00003a64    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00003aa0    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00003ada    00000002     --HOLE-- [fill = 0]
                  00003adc    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00003b16    00000002     --HOLE-- [fill = 0]
                  00003b18    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00003b50    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003b84    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003bb8    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00003bec    00000034     Task_App.o (.text.Task_IdleFunction)
                  00003c20    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  00003c50    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00003c80    0000002c     iqmath.a : _IQNmpy.o (.text._IQ24mpy)
                  00003cac    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00003cd8    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00003d04    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00003d30    0000002a     PID.o (.text.PID_Init)
                  00003d5a    00000028     OLED.o (.text.DL_Common_updateReg)
                  00003d82    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00003daa    00000002     --HOLE-- [fill = 0]
                  00003dac    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00003dd4    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00003dfc    00000028     SysTick.o (.text.SysTick_Increasment)
                  00003e24    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00003e4c    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00003e72    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00003e98    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00003ebc    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00003ee0    00000022     PID.o (.text.PID_SetParams)
                  00003f02    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00003f24    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00003f44    00000020     SysTick.o (.text.Delay)
                  00003f64    00000020     main.o (.text.main)
                  00003f84    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00003fa2    00000002     --HOLE-- [fill = 0]
                  00003fa4    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00003fc0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00003fdc    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00003ff8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00004014    0000001c     Interrupt.o (.text.DL_GPIO_enableInterrupt)
                  00004030    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  0000404c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00004068    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00004084    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  000040a0    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  000040bc    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  000040d8    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  000040f4    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00004110    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  0000412c    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00004148    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  00004160    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00004178    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00004190    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  000041a8    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  000041c0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  000041d8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  000041f0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00004208    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00004220    00000018     Task_App.o (.text.DL_GPIO_setPins)
                  00004238    00000018     Tracker.o (.text.DL_GPIO_setPins)
                  00004250    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00004268    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00004280    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  00004298    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  000042b0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  000042c8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  000042e0    00000018     OLED.o (.text.DL_I2C_enablePower)
                  000042f8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00004310    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  00004328    00000018     OLED.o (.text.DL_I2C_reset)
                  00004340    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00004358    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00004370    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00004388    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  000043a0    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000043b8    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000043d0    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000043e8    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00004400    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00004418    00000018     libc.a : vsprintf.c.obj (.text._outs)
                  00004430    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00004446    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  0000445c    00000016     OLED.o (.text.DL_GPIO_readPins)
                  00004472    00000016     Tracker.o (.text.DL_GPIO_readPins)
                  00004488    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  0000449e    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  000044b2    00000014     Task_App.o (.text.DL_GPIO_clearPins)
                  000044c6    00000014     Tracker.o (.text.DL_GPIO_clearPins)
                  000044da    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  000044ee    00000002     --HOLE-- [fill = 0]
                  000044f0    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  00004504    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00004518    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  0000452c    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00004540    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00004554    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00004568    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  0000457c    00000012            : memcpy16.S.obj (.text:TI_memcpy_small)
                  0000458e    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000045a0    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  000045b0    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  000045c0    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  000045d0    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  000045e0    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  000045ee    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  000045fc    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  0000460a    00000002     --HOLE-- [fill = 0]
                  0000460c    0000000c     SysTick.o (.text.Sys_GetTick)
                  00004618    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00004622    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  0000462c    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  0000463c    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00004646    0000000a            : vsprintf.c.obj (.text._outc)
                  00004650    00000008     Interrupt.o (.text.SysTick_Handler)
                  00004658    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00004660    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00004668    00000006     libc.a : exit.c.obj (.text:abort)
                  0000466e    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00004672    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00004676    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  0000467a    00000002     --HOLE-- [fill = 0]
                  0000467c    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  0000468c    00000004            : pre_init.c.obj (.text._system_pre_init)

.cinit     0    00005080    00000060     
                  00005080    00000038     (.cinit..data.load) [load image, compression = lzss]
                  000050b8    0000000c     (__TI_handler_table)
                  000050c4    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000050cc    00000010     (__TI_cinit_table)
                  000050dc    00000004     --HOLE-- [fill = 0]

.rodata    0    00004690    000009f0     
                  00004690    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  00004c80    00000228     OLED_Font.o (.rodata.asc2_0806)
                  00004ea8    00000008     ti_msp_dl_config.o (.rodata.gMotorAFrontConfig)
                  00004eb0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00004fb1    00000041     iqmath.a : _IQNtables.o (.rodata._IQ6div_lookup)
                  00004ff2    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00004ff4    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  0000501c    00000012     Task_App.o (.rodata.str1.16020955549137178199.1)
                  0000502e    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  0000503f    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00005050    00000008     ti_msp_dl_config.o (.rodata.gMotorBFrontConfig)
                  00005058    00000008     Task_App.o (.rodata.str1.12629676409056169537.1)
                  00005060    00000006     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00005066    00000005     Task_App.o (.rodata.str1.8896853068034818020.1)
                  0000506b    00000004     Task_App.o (.rodata.str1.10635198597896025474.1)
                  0000506f    00000003     ti_msp_dl_config.o (.rodata.gMotorAFrontClockConfig)
                  00005072    00000003     ti_msp_dl_config.o (.rodata.gMotorBFrontClockConfig)
                  00005075    0000000b     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.sysmem    0    20200000    00000400     UNINITIALIZED
                  20200000    00000010     libc.a : memory.c.obj (.sysmem)
                  20200010    000003f0     --HOLE--

.bss       0    20200400    00000236     UNINITIALIZED
                  20200400    000000f0     Task.o (.bss.Task_Schedule)
                  202004f0    000000a0     (.common:gMotorAFrontBackup)
                  20200590    000000a0     (.common:gMotorBFrontBackup)
                  20200630    00000004     (.common:ExISR_Flag)
                  20200634    00000001     Task_App.o (.bss.Task_Key.Key_Old)
                  20200635    00000001     (.common:ret)

.data      0    20200638    000000bc     UNINITIALIZED
                  20200638    00000048     Motor.o (.data.Motor_Font_Left)
                  20200680    00000048     Motor.o (.data.Motor_Font_Right)
                  202006c8    00000008     Task_App.o (.data.Data_Tracker_Input)
                  202006d0    00000008     Task_App.o (.data.Motor)
                  202006d8    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202006dc    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  202006e0    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  202006e4    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202006e8    00000004     SysTick.o (.data.delayTick)
                  202006ec    00000004     SysTick.o (.data.uwTick)
                  202006f0    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  202006f2    00000001     Task.o (.data.Task_Num)
                  202006f3    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             2442    64        320    
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         32      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2482    256       320    
                                                               
    .\APP\Src\
       Task_App.o                     900     41        31     
       Interrupt.o                    422     0         5      
    +--+------------------------------+-------+---------+---------+
       Total:                         1322    41        36     
                                                               
    .\BSP\Src\
       OLED_Font.o                    0       2072      0      
       OLED.o                         1858    0         0      
       Task.o                         674     0         241    
       Motor.o                        580     0         144    
       Tracker.o                      414     0         1      
       PID.o                          400     0         0      
       SysTick.o                      200     0         8      
       Key_Led.o                      122     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4248    2072      394    
                                                               
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_timer.o                     356     0         0      
       dl_i2c.o                       132     0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         886     0         0      
                                                               
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/iqmath/lib/ticlang/m0p/rts/mspm0g1x0x_g3x0x/iqmath.a
       _IQNdiv.o                      268     0         0      
       _IQNtables.o                   0       65        0      
       _IQNtoF.o                      48      0         0      
       _IQNmpy.o                      44      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         360     65        0      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       qsort.c.obj                    308     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       vsprintf.c.obj                 78      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5736    291       4      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       fixunsdfsi.S.obj               66      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsidf.S.obj              36      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memset.S.obj             14      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2802    0         0      
                                                               
       Heap:                          0       0         1024   
       Stack:                         0       0         512    
       Linker Generated:              0       92        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   17840   2817      2290   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000050cc records: 2, size/record: 8, table size: 16
	.data: load addr=00005080, load size=00000038 bytes, run addr=20200638, run size=000000bc bytes, compression=lzss
	.bss: load addr=000050c4, load size=00000008 bytes, run addr=20200400, run size=00000236 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000050b8 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   000011f9     0000462c     0000462a   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00003e25     0000467c     00004676   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[2 trampolines]
[2 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
0000466f  ADC0_IRQHandler                      
0000466f  ADC1_IRQHandler                      
0000466f  AES_IRQHandler                       
00004672  C$$EXIT                              
0000466f  CANFD0_IRQHandler                    
0000466f  DAC0_IRQHandler                      
00004619  DL_Common_delayCycles                
000031b1  DL_I2C_fillControllerTXFIFO          
00003e73  DL_I2C_setClockConfig                
000021cd  DL_SYSCTL_configSYSPLL               
00002f61  DL_SYSCTL_setHFCLKSourceHFXTParams   
000036f1  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00001e15  DL_Timer_initFourCCPWMMode           
00004111  DL_Timer_setCaptCompUpdateMethod     
000043e9  DL_Timer_setCaptureCompareOutCtl     
000045b1  DL_Timer_setCaptureCompareValue      
0000412d  DL_Timer_setClockConfig              
0000466f  DMA_IRQHandler                       
202006d8  Data_MotorEncoder                    
202006dc  Data_Motor_TarSpeed                  
202006c8  Data_Tracker_Input                   
202006e0  Data_Tracker_Offset                  
0000466f  Default_Handler                      
00003f45  Delay                                
20200630  ExISR_Flag                           
0000466f  GROUP0_IRQHandler                    
000025e1  GROUP1_IRQHandler                    
00004673  HOSTexit                             
0000466f  HardFault_Handler                    
0000466f  I2C0_IRQHandler                      
0000466f  I2C1_IRQHandler                      
00002e27  I2C_OLED_Clear                       
000039b1  I2C_OLED_Set_Pos                     
00002735  I2C_OLED_WR_Byte                     
000030f1  I2C_OLED_i2c_sda_unlock              
000037bd  Interrupt_Init                       
00002fc5  Key_Read                             
202006d0  Motor                                
20200638  Motor_Font_Left                      
20200680  Motor_Font_Right                     
0000365f  Motor_GetSpeed                       
00002459  Motor_SetDuty                        
00002b6d  Motor_Start                          
0000466f  NMI_Handler                          
00001aed  OLED_Init                            
000035c9  OLED_Printf                          
0000189d  OLED_ShowChar                        
00002db9  OLED_ShowString                      
00003d31  PID_Init                             
000014e9  PID_SProsc                           
00003ee1  PID_SetParams                        
0000466f  PendSV_Handler                       
0000466f  RTC_IRQHandler                       
00004677  Reset_Handler                        
0000466f  SPI0_IRQHandler                      
0000466f  SPI1_IRQHandler                      
0000466f  SVC_Handler                          
00000e8d  SYSCFG_DL_GPIO_init                  
00003151  SYSCFG_DL_I2C_OLED_init              
000027cd  SYSCFG_DL_MotorAFront_init           
00002859  SYSCFG_DL_MotorBFront_init           
00003481  SYSCFG_DL_SYSCTL_init                
000045c1  SYSCFG_DL_SYSTICK_init               
00003bb9  SYSCFG_DL_init                       
00002a75  SYSCFG_DL_initPower                  
00004651  SysTick_Handler                      
00003dfd  SysTick_Increasment                  
0000460d  Sys_GetTick                          
0000466f  TIMA0_IRQHandler                     
0000466f  TIMA1_IRQHandler                     
0000466f  TIMG0_IRQHandler                     
0000466f  TIMG12_IRQHandler                    
0000466f  TIMG6_IRQHandler                     
0000466f  TIMG7_IRQHandler                     
0000466f  TIMG8_IRQHandler                     
0000457d  TI_memcpy_small                      
000045fd  TI_memset_small                      
0000252d  Task_Add                             
00003bed  Task_IdleFunction                    
00002be5  Task_Init                            
00001f19  Task_Key                             
00002001  Task_Motor_PID                       
000029f5  Task_OLED                            
00001049  Task_Start                           
00003211  Task_Tracker                         
0000138d  Tracker_Read                         
0000466f  UART0_IRQHandler                     
0000466f  UART1_IRQHandler                     
0000466f  UART2_IRQHandler                     
0000466f  UART3_IRQHandler                     
00001bfd  _IQ24div                             
00003c81  _IQ24mpy                             
00003c21  _IQ24toF                             
00004fb1  _IQ6div_lookup                       
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000400  __SYSMEM_SIZE                        
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
000050cc  __TI_CINIT_Base                      
000050dc  __TI_CINIT_Limit                     
000050dc  __TI_CINIT_Warm                      
000050b8  __TI_Handler_Table_Base              
000050c4  __TI_Handler_Table_Limit             
00003a65  __TI_auto_init_nobinit_nopinit       
00002af1  __TI_decompress_lzss                 
0000458f  __TI_decompress_none                 
00003325  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00004489  __TI_zero_init_nomemset              
00001203  __adddf3                             
0000238b  __addsf3                             
00004eb0  __aeabi_ctype_table_                 
00004eb0  __aeabi_ctype_table_C                
00002cd1  __aeabi_d2f                          
00003615  __aeabi_d2iz                         
00003779  __aeabi_d2uiz                        
00001203  __aeabi_dadd                         
00003029  __aeabi_dcmpeq                       
00003065  __aeabi_dcmpge                       
00003079  __aeabi_dcmpgt                       
00003051  __aeabi_dcmple                       
0000303d  __aeabi_dcmplt                       
00001d09  __aeabi_ddiv                         
000020e9  __aeabi_dmul                         
000011f9  __aeabi_dsub                         
202006e4  __aeabi_errno                        
00004659  __aeabi_errno_addr                   
0000383d  __aeabi_f2d                          
00003b19  __aeabi_f2iz                         
0000238b  __aeabi_fadd                         
0000308d  __aeabi_fcmpeq                       
000030c9  __aeabi_fcmpge                       
000030dd  __aeabi_fcmpgt                       
000030b5  __aeabi_fcmple                       
000030a1  __aeabi_fcmplt                       
000028e5  __aeabi_fmul                         
00002381  __aeabi_fsub                         
00003cd9  __aeabi_i2d                          
000039ed  __aeabi_i2f                          
000033d5  __aeabi_idiv                         
0000138b  __aeabi_idiv0                        
000033d5  __aeabi_idivmod                      
00002733  __aeabi_ldiv0                        
00003f85  __aeabi_llsl                         
00003ebd  __aeabi_lmul                         
00004661  __aeabi_memcpy                       
00004661  __aeabi_memcpy4                      
00004661  __aeabi_memcpy8                      
000045e1  __aeabi_memset                       
000045e1  __aeabi_memset4                      
000045e1  __aeabi_memset8                      
00003e99  __aeabi_ui2d                         
000037fd  __aeabi_uidiv                        
000037fd  __aeabi_uidivmod                     
00004555  __aeabi_uldivmod                     
00003f85  __ashldi3                            
ffffffff  __binit__                            
00002e91  __cmpdf2                             
00003aa1  __cmpsf2                             
00001d09  __divdf3                             
00002e91  __eqdf2                              
00003aa1  __eqsf2                              
0000383d  __extendsfdf2                        
00003615  __fixdfsi                            
00003b19  __fixsfsi                            
00003779  __fixunsdfsi                         
00003cd9  __floatsidf                          
000039ed  __floatsisf                          
00003e99  __floatunsidf                        
00002c5d  __gedf2                              
00003a29  __gesf2                              
00002c5d  __gtdf2                              
00003a29  __gtsf2                              
00002e91  __ledf2                              
00003aa1  __lesf2                              
00002e91  __ltdf2                              
00003aa1  __ltsf2                              
UNDEFED   __mpu_init                           
000020e9  __muldf3                             
00003ebd  __muldi3                             
00003add  __muldsi3                            
000028e5  __mulsf3                             
00002e91  __nedf2                              
00003aa1  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000011f9  __subdf3                             
00002381  __subsf3                             
00002cd1  __truncdfsf2                         
00002691  __udivmoddi4                         
00003e25  _c_int00_noargs                      
20200000  _sys_memory                          
UNDEFED   _system_post_cinit                   
0000468d  _system_pre_init                     
00004669  abort                                
00004c80  asc2_0806                            
00004690  asc2_1608                            
0000387d  atoi                                 
ffffffff  binit                                
202006e8  delayTick                            
00002d45  delay_us                             
202006f3  enable_group1_irq                    
0000326d  frexp                                
0000326d  frexpl                               
202004f0  gMotorAFrontBackup                   
20200590  gMotorBFrontBackup                   
00000000  interruptVectors                     
000022a9  ldexp                                
000022a9  ldexpl                               
00003f65  main                                 
00003f03  memccpy                              
00001769  qsort                                
20200635  ret                                  
000022a9  scalbn                               
000022a9  scalbnl                              
202006ec  uwTick                               
00003d05  vsprintf                             
000045d1  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000400  __SYSMEM_SIZE                        
00000e8d  SYSCFG_DL_GPIO_init                  
00001049  Task_Start                           
000011f9  __aeabi_dsub                         
000011f9  __subdf3                             
00001203  __adddf3                             
00001203  __aeabi_dadd                         
0000138b  __aeabi_idiv0                        
0000138d  Tracker_Read                         
000014e9  PID_SProsc                           
00001769  qsort                                
0000189d  OLED_ShowChar                        
00001aed  OLED_Init                            
00001bfd  _IQ24div                             
00001d09  __aeabi_ddiv                         
00001d09  __divdf3                             
00001e15  DL_Timer_initFourCCPWMMode           
00001f19  Task_Key                             
00002001  Task_Motor_PID                       
000020e9  __aeabi_dmul                         
000020e9  __muldf3                             
000021cd  DL_SYSCTL_configSYSPLL               
000022a9  ldexp                                
000022a9  ldexpl                               
000022a9  scalbn                               
000022a9  scalbnl                              
00002381  __aeabi_fsub                         
00002381  __subsf3                             
0000238b  __addsf3                             
0000238b  __aeabi_fadd                         
00002459  Motor_SetDuty                        
0000252d  Task_Add                             
000025e1  GROUP1_IRQHandler                    
00002691  __udivmoddi4                         
00002733  __aeabi_ldiv0                        
00002735  I2C_OLED_WR_Byte                     
000027cd  SYSCFG_DL_MotorAFront_init           
00002859  SYSCFG_DL_MotorBFront_init           
000028e5  __aeabi_fmul                         
000028e5  __mulsf3                             
000029f5  Task_OLED                            
00002a75  SYSCFG_DL_initPower                  
00002af1  __TI_decompress_lzss                 
00002b6d  Motor_Start                          
00002be5  Task_Init                            
00002c5d  __gedf2                              
00002c5d  __gtdf2                              
00002cd1  __aeabi_d2f                          
00002cd1  __truncdfsf2                         
00002d45  delay_us                             
00002db9  OLED_ShowString                      
00002e27  I2C_OLED_Clear                       
00002e91  __cmpdf2                             
00002e91  __eqdf2                              
00002e91  __ledf2                              
00002e91  __ltdf2                              
00002e91  __nedf2                              
00002f61  DL_SYSCTL_setHFCLKSourceHFXTParams   
00002fc5  Key_Read                             
00003029  __aeabi_dcmpeq                       
0000303d  __aeabi_dcmplt                       
00003051  __aeabi_dcmple                       
00003065  __aeabi_dcmpge                       
00003079  __aeabi_dcmpgt                       
0000308d  __aeabi_fcmpeq                       
000030a1  __aeabi_fcmplt                       
000030b5  __aeabi_fcmple                       
000030c9  __aeabi_fcmpge                       
000030dd  __aeabi_fcmpgt                       
000030f1  I2C_OLED_i2c_sda_unlock              
00003151  SYSCFG_DL_I2C_OLED_init              
000031b1  DL_I2C_fillControllerTXFIFO          
00003211  Task_Tracker                         
0000326d  frexp                                
0000326d  frexpl                               
00003325  __TI_ltoa                            
000033d5  __aeabi_idiv                         
000033d5  __aeabi_idivmod                      
00003481  SYSCFG_DL_SYSCTL_init                
000035c9  OLED_Printf                          
00003615  __aeabi_d2iz                         
00003615  __fixdfsi                            
0000365f  Motor_GetSpeed                       
000036f1  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00003779  __aeabi_d2uiz                        
00003779  __fixunsdfsi                         
000037bd  Interrupt_Init                       
000037fd  __aeabi_uidiv                        
000037fd  __aeabi_uidivmod                     
0000383d  __aeabi_f2d                          
0000383d  __extendsfdf2                        
0000387d  atoi                                 
000039b1  I2C_OLED_Set_Pos                     
000039ed  __aeabi_i2f                          
000039ed  __floatsisf                          
00003a29  __gesf2                              
00003a29  __gtsf2                              
00003a65  __TI_auto_init_nobinit_nopinit       
00003aa1  __cmpsf2                             
00003aa1  __eqsf2                              
00003aa1  __lesf2                              
00003aa1  __ltsf2                              
00003aa1  __nesf2                              
00003add  __muldsi3                            
00003b19  __aeabi_f2iz                         
00003b19  __fixsfsi                            
00003bb9  SYSCFG_DL_init                       
00003bed  Task_IdleFunction                    
00003c21  _IQ24toF                             
00003c81  _IQ24mpy                             
00003cd9  __aeabi_i2d                          
00003cd9  __floatsidf                          
00003d05  vsprintf                             
00003d31  PID_Init                             
00003dfd  SysTick_Increasment                  
00003e25  _c_int00_noargs                      
00003e73  DL_I2C_setClockConfig                
00003e99  __aeabi_ui2d                         
00003e99  __floatunsidf                        
00003ebd  __aeabi_lmul                         
00003ebd  __muldi3                             
00003ee1  PID_SetParams                        
00003f03  memccpy                              
00003f45  Delay                                
00003f65  main                                 
00003f85  __aeabi_llsl                         
00003f85  __ashldi3                            
00004111  DL_Timer_setCaptCompUpdateMethod     
0000412d  DL_Timer_setClockConfig              
000043e9  DL_Timer_setCaptureCompareOutCtl     
00004489  __TI_zero_init_nomemset              
00004555  __aeabi_uldivmod                     
0000457d  TI_memcpy_small                      
0000458f  __TI_decompress_none                 
000045b1  DL_Timer_setCaptureCompareValue      
000045c1  SYSCFG_DL_SYSTICK_init               
000045d1  wcslen                               
000045e1  __aeabi_memset                       
000045e1  __aeabi_memset4                      
000045e1  __aeabi_memset8                      
000045fd  TI_memset_small                      
0000460d  Sys_GetTick                          
00004619  DL_Common_delayCycles                
00004651  SysTick_Handler                      
00004659  __aeabi_errno_addr                   
00004661  __aeabi_memcpy                       
00004661  __aeabi_memcpy4                      
00004661  __aeabi_memcpy8                      
00004669  abort                                
0000466f  ADC0_IRQHandler                      
0000466f  ADC1_IRQHandler                      
0000466f  AES_IRQHandler                       
0000466f  CANFD0_IRQHandler                    
0000466f  DAC0_IRQHandler                      
0000466f  DMA_IRQHandler                       
0000466f  Default_Handler                      
0000466f  GROUP0_IRQHandler                    
0000466f  HardFault_Handler                    
0000466f  I2C0_IRQHandler                      
0000466f  I2C1_IRQHandler                      
0000466f  NMI_Handler                          
0000466f  PendSV_Handler                       
0000466f  RTC_IRQHandler                       
0000466f  SPI0_IRQHandler                      
0000466f  SPI1_IRQHandler                      
0000466f  SVC_Handler                          
0000466f  TIMA0_IRQHandler                     
0000466f  TIMA1_IRQHandler                     
0000466f  TIMG0_IRQHandler                     
0000466f  TIMG12_IRQHandler                    
0000466f  TIMG6_IRQHandler                     
0000466f  TIMG7_IRQHandler                     
0000466f  TIMG8_IRQHandler                     
0000466f  UART0_IRQHandler                     
0000466f  UART1_IRQHandler                     
0000466f  UART2_IRQHandler                     
0000466f  UART3_IRQHandler                     
00004672  C$$EXIT                              
00004673  HOSTexit                             
00004677  Reset_Handler                        
0000468d  _system_pre_init                     
00004690  asc2_1608                            
00004c80  asc2_0806                            
00004eb0  __aeabi_ctype_table_                 
00004eb0  __aeabi_ctype_table_C                
00004fb1  _IQ6div_lookup                       
000050b8  __TI_Handler_Table_Base              
000050c4  __TI_Handler_Table_Limit             
000050cc  __TI_CINIT_Base                      
000050dc  __TI_CINIT_Limit                     
000050dc  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  _sys_memory                          
202004f0  gMotorAFrontBackup                   
20200590  gMotorBFrontBackup                   
20200630  ExISR_Flag                           
20200635  ret                                  
20200638  Motor_Font_Left                      
20200680  Motor_Font_Right                     
202006c8  Data_Tracker_Input                   
202006d0  Motor                                
202006d8  Data_MotorEncoder                    
202006dc  Data_Motor_TarSpeed                  
202006e0  Data_Tracker_Offset                  
202006e4  __aeabi_errno                        
202006e8  delayTick                            
202006ec  uwTick                               
202006f3  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[233 symbols]
