******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Thu Jul 31 22:15:59 2025

OUTPUT FILE NAME:   <TI_CAR1.4.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00003b81


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00004dd8  0001b228  R  X
  SRAM                  20200000   00008000  000008e7  00007719  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00004dd8   00004dd8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00004310   00004310    r-x .text
  000043d0    000043d0    000009b0   000009b0    r-- .rodata
  00004d80    00004d80    00000058   00000058    r-- .cinit
20200000    20200000    000006e9   00000000    rw-
  20200000    20200000    00000400   00000000    rw- .sysmem
  20200400    20200400    00000236   00000000    rw- .bss
  20200638    20200638    000000b1   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00004310     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000220            : _printfi.c.obj (.text._pconv_a)
                  00000cb0    000001dc            : _printfi.c.obj (.text._pconv_g)
                  00000e8c    000001b0     Task.o (.text.Task_Start)
                  0000103c    00000198     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000011d4    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00001366    00000002     Task.o (.text.Task_IdleFunction)
                  00001368    00000144     PID.o (.text.PID_SProsc)
                  000014ac    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  000015e8    00000134            : qsort.c.obj (.text.qsort)
                  0000171c    00000130     OLED.o (.text.OLED_ShowChar)
                  0000184c    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  0000196c    00000110     OLED.o (.text.OLED_Init)
                  00001a7c    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001b88    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00001c8c    000000e8     Task_App.o (.text.Task_Motor_PID)
                  00001d74    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00001e58    000000e0     Task_App.o (.text.Task_Key)
                  00001f38    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00002014    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  000020ec    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  000021c4    000000d4     Motor.o (.text.Motor_SetDuty)
                  00002298    000000b4     Task.o (.text.Task_Add)
                  0000234c    000000b0     Interrupt.o (.text.GROUP1_IRQHandler)
                  000023fc    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  0000249e    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000024a0    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  00002538    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorAFront_init)
                  000025c4    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorBFront_init)
                  00002650    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000026dc    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00002768    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  000027ec    00000080     Task_App.o (.text.Task_OLED)
                  0000286c    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000028e8    00000078     Motor.o (.text.Motor_Start)
                  00002960    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  000029d4    0000000c     SysTick.o (.text.Sys_GetTick)
                  000029e0    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00002a54    0000006e     OLED.o (.text.OLED_ShowString)
                  00002ac2    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  00002b2c    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00002b94    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00002bfa    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00002bfc    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00002c60    00000064     Key_Led.o (.text.Key_Read)
                  00002cc4    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00002d26    00000002     --HOLE-- [fill = 0]
                  00002d28    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00002d8a    00000002     --HOLE-- [fill = 0]
                  00002d8c    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00002dec    00000060     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00002e4c    00000060     Task_App.o (.text.Task_Init)
                  00002eac    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00002f0a    00000002     --HOLE-- [fill = 0]
                  00002f0c    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00002f68    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  00002fc4    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  0000301c    00000058            : _printfi.c.obj (.text._pconv_f)
                  00003074    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  000030ca    00000002     --HOLE-- [fill = 0]
                  000030cc    00000054     Motor.o (.text.CalculateDutyValue)
                  00003120    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00003174    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  000031c6    00000002     --HOLE-- [fill = 0]
                  000031c8    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  00003218    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00003268    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  000032b4    0000004c     OLED.o (.text.OLED_Printf)
                  00003300    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  0000334c    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00003396    00000002     --HOLE-- [fill = 0]
                  00003398    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  000033e2    00000048     Motor.o (.text.Motor_GetSpeed)
                  0000342a    00000002     --HOLE-- [fill = 0]
                  0000342c    00000048     OLED.o (.text.mspm0_i2c_disable)
                  00003474    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000034b8    00000044     Motor.o (.text.SetPWMValue)
                  000034fc    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  0000353e    00000002     --HOLE-- [fill = 0]
                  00003540    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00003580    00000040     Interrupt.o (.text.Interrupt_Init)
                  000035c0    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00003600    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00003640    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00003680    0000003e     Task.o (.text.Task_CMP)
                  000036be    00000002     --HOLE-- [fill = 0]
                  000036c0    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000036fc    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00003738    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00003774    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  000037b0    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  000037ec    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00003828    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00003864    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  0000389e    00000002     --HOLE-- [fill = 0]
                  000038a0    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  000038da    00000002     --HOLE-- [fill = 0]
                  000038dc    00000038     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00003914    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003948    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  0000397c    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  000039ac    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  000039dc    0000002c     iqmath.a : _IQNmpy.o (.text._IQ24mpy)
                  00003a08    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00003a34    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00003a60    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00003a8c    0000002a     PID.o (.text.PID_Init)
                  00003ab6    00000028     OLED.o (.text.DL_Common_updateReg)
                  00003ade    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00003b06    00000002     --HOLE-- [fill = 0]
                  00003b08    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00003b30    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00003b58    00000028     SysTick.o (.text.SysTick_Increasment)
                  00003b80    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00003ba8    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00003bce    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00003bf4    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00003c18    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00003c3c    00000022     PID.o (.text.PID_SetParams)
                  00003c5e    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00003c80    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00003ca0    00000020     SysTick.o (.text.Delay)
                  00003cc0    00000020     main.o (.text.main)
                  00003ce0    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00003cfe    00000002     --HOLE-- [fill = 0]
                  00003d00    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00003d1c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00003d38    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00003d54    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00003d70    0000001c     Interrupt.o (.text.DL_GPIO_enableInterrupt)
                  00003d8c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00003da8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00003dc4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00003de0    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00003dfc    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  00003e18    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00003e34    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00003e50    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00003e6c    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00003e88    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00003ea4    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00003ebc    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00003ed4    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  00003eec    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00003f04    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00003f1c    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00003f34    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  00003f4c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00003f64    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00003f7c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00003f94    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00003fac    00000018     Task_App.o (.text.DL_GPIO_setPins)
                  00003fc4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00003fdc    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  00003ff4    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  0000400c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00004024    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  0000403c    00000018     OLED.o (.text.DL_I2C_enablePower)
                  00004054    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  0000406c    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  00004084    00000018     OLED.o (.text.DL_I2C_reset)
                  0000409c    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  000040b4    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  000040cc    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  000040e4    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  000040fc    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00004114    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  0000412c    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00004144    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  0000415c    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00004174    00000018     libc.a : vsprintf.c.obj (.text._outs)
                  0000418c    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  000041a2    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  000041b8    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  000041ce    00000016     OLED.o (.text.DL_GPIO_readPins)
                  000041e4    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000041fa    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  0000420e    00000014     Task_App.o (.text.DL_GPIO_clearPins)
                  00004222    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00004236    00000002     --HOLE-- [fill = 0]
                  00004238    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  0000424c    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00004260    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00004274    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00004288    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  0000429c    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  000042b0    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  000042c4    00000012            : memcpy16.S.obj (.text:TI_memcpy_small)
                  000042d6    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000042e8    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  000042f8    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00004308    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00004318    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00004328    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00004336    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00004344    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00004352    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000435c    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00004366    00000002     --HOLE-- [fill = 0]
                  00004368    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00004378    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00004382    0000000a            : vsprintf.c.obj (.text._outc)
                  0000438c    00000008     Interrupt.o (.text.SysTick_Handler)
                  00004394    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  0000439c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000043a4    00000006     libc.a : exit.c.obj (.text:abort)
                  000043aa    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000043ae    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000043b2    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000043b6    00000002     --HOLE-- [fill = 0]
                  000043b8    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  000043c8    00000004            : pre_init.c.obj (.text._system_pre_init)
                  000043cc    00000004     --HOLE-- [fill = 0]

.cinit     0    00004d80    00000058     
                  00004d80    00000031     (.cinit..data.load) [load image, compression = lzss]
                  00004db1    00000003     --HOLE-- [fill = 0]
                  00004db4    0000000c     (__TI_handler_table)
                  00004dc0    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00004dc8    00000010     (__TI_cinit_table)

.rodata    0    000043d0    000009b0     
                  000043d0    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  000049c0    00000228     OLED_Font.o (.rodata.asc2_0806)
                  00004be8    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  00004bf0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00004cf1    00000003     ti_msp_dl_config.o (.rodata.gMotorAFrontClockConfig)
                  00004cf4    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00004d1c    00000012     Task_App.o (.rodata.str1.8896853068034818020.1)
                  00004d2e    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00004d3f    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00004d50    00000008     ti_msp_dl_config.o (.rodata.gMotorAFrontConfig)
                  00004d58    00000008     ti_msp_dl_config.o (.rodata.gMotorBFrontConfig)
                  00004d60    00000006     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00004d66    00000005     Task_App.o (.rodata.str1.10635198597896025474.1)
                  00004d6b    00000004     Task_App.o (.rodata.str1.12629676409056169537.1)
                  00004d6f    00000003     ti_msp_dl_config.o (.rodata.gMotorBFrontClockConfig)
                  00004d72    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00004d74    0000000c     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.sysmem    0    20200000    00000400     UNINITIALIZED
                  20200000    00000010     libc.a : memory.c.obj (.sysmem)
                  20200010    000003f0     --HOLE--

.bss       0    20200400    00000236     UNINITIALIZED
                  20200400    000000f0     Task.o (.bss.Task_Schedule)
                  202004f0    000000a0     (.common:gMotorAFrontBackup)
                  20200590    000000a0     (.common:gMotorBFrontBackup)
                  20200630    00000004     (.common:ExISR_Flag)
                  20200634    00000001     Task_App.o (.bss.Task_Key.Key_Old)
                  20200635    00000001     (.common:HD_count)

.data      0    20200638    000000b1     UNINITIALIZED
                  20200638    00000048     Motor.o (.data.Motor_Font_Left)
                  20200680    00000048     Motor.o (.data.Motor_Font_Right)
                  202006c8    00000008     Task_App.o (.data.Motor)
                  202006d0    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202006d4    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  202006d8    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  202006dc    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202006e0    00000004     SysTick.o (.data.delayTick)
                  202006e4    00000004     SysTick.o (.data.uwTick)
                  202006e8    00000001     Task.o (.data.Task_Num)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             2698    72        320    
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         32      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2738    264       320    
                                                               
    .\APP\Src\
       Task_App.o                     724     33        21     
       Interrupt.o                    422     0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         1146    33        25     
                                                               
    .\BSP\Src\
       OLED_Font.o                    0       2072      0      
       OLED.o                         1858    0         0      
       Task.o                         676     0         241    
       Motor.o                        580     0         144    
       PID.o                          400     0         0      
       Key_Led.o                      122     0         0      
       SysTick.o                      84      0         8      
       Tracker.o                      0       0         1      
    +--+------------------------------+-------+---------+---------+
       Total:                         3720    2072      394    
                                                               
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_timer.o                     356     0         0      
       dl_i2c.o                       132     0         0      
       dl_adc12.o                     64      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         950     0         0      
                                                               
    D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/iqmath/lib/ticlang/m0p/rts/mspm0g1x0x_g3x0x/iqmath.a
       _IQNtoF.o                      48      0         0      
       _IQNmpy.o                      44      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         92      0         0      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       qsort.c.obj                    308     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       vsprintf.c.obj                 78      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5736    291       4      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\desk\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       fixunsdfsi.S.obj               66      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsidf.S.obj              36      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memset.S.obj             14      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2746    0         0      
                                                               
       Heap:                          0       0         1024   
       Stack:                         0       0         512    
       Linker Generated:              0       85        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   17132   2745      2279   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00004dc8 records: 2, size/record: 8, table size: 16
	.data: load addr=00004d80, load size=00000031 bytes, run addr=20200638, run size=000000b1 bytes, compression=lzss
	.bss: load addr=00004dc0, load size=00000008 bytes, run addr=20200400, run size=00000236 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00004db4 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   000011d5     00004368     00004364   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00003b81     000043b8     000043b2   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[2 trampolines]
[2 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
000043ab  ADC0_IRQHandler                      
000043ab  ADC1_IRQHandler                      
000043ab  AES_IRQHandler                       
000043ae  C$$EXIT                              
000043ab  CANFD0_IRQHandler                    
000043ab  DAC0_IRQHandler                      
00003541  DL_ADC12_setClockConfig              
00004353  DL_Common_delayCycles                
00002ead  DL_I2C_fillControllerTXFIFO          
00003bcf  DL_I2C_setClockConfig                
00001f39  DL_SYSCTL_configSYSPLL               
00002bfd  DL_SYSCTL_setHFCLKSourceHFXTParams   
00003475  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00001b89  DL_Timer_initFourCCPWMMode           
00003e6d  DL_Timer_setCaptCompUpdateMethod     
00004145  DL_Timer_setCaptureCompareOutCtl     
000042f9  DL_Timer_setCaptureCompareValue      
00003e89  DL_Timer_setClockConfig              
000043ab  DMA_IRQHandler                       
202006d0  Data_MotorEncoder                    
202006d4  Data_Motor_TarSpeed                  
202006d8  Data_Tracker_Offset                  
000043ab  Default_Handler                      
00003ca1  Delay                                
20200630  ExISR_Flag                           
000043ab  GROUP0_IRQHandler                    
0000234d  GROUP1_IRQHandler                    
20200635  HD_count                             
000043af  HOSTexit                             
000043ab  HardFault_Handler                    
000043ab  I2C0_IRQHandler                      
000043ab  I2C1_IRQHandler                      
00002ac3  I2C_OLED_Clear                       
00003775  I2C_OLED_Set_Pos                     
000024a1  I2C_OLED_WR_Byte                     
00002d8d  I2C_OLED_i2c_sda_unlock              
00003581  Interrupt_Init                       
00002c61  Key_Read                             
202006c8  Motor                                
20200638  Motor_Font_Left                      
20200680  Motor_Font_Right                     
000033e3  Motor_GetSpeed                       
000021c5  Motor_SetDuty                        
000028e9  Motor_Start                          
000043ab  NMI_Handler                          
0000196d  OLED_Init                            
000032b5  OLED_Printf                          
0000171d  OLED_ShowChar                        
00002a55  OLED_ShowString                      
00003a8d  PID_Init                             
00001369  PID_SProsc                           
00003c3d  PID_SetParams                        
000043ab  PendSV_Handler                       
000043ab  RTC_IRQHandler                       
000043b3  Reset_Handler                        
000043ab  SPI0_IRQHandler                      
000043ab  SPI1_IRQHandler                      
000043ab  SVC_Handler                          
00003301  SYSCFG_DL_ADC1_init                  
0000103d  SYSCFG_DL_GPIO_init                  
00002ded  SYSCFG_DL_I2C_OLED_init              
00002539  SYSCFG_DL_MotorAFront_init           
000025c5  SYSCFG_DL_MotorBFront_init           
00003121  SYSCFG_DL_SYSCTL_init                
00004309  SYSCFG_DL_SYSTICK_init               
000038dd  SYSCFG_DL_init                       
00002651  SYSCFG_DL_initPower                  
0000438d  SysTick_Handler                      
00003b59  SysTick_Increasment                  
000029d5  Sys_GetTick                          
000043ab  TIMA0_IRQHandler                     
000043ab  TIMA1_IRQHandler                     
000043ab  TIMG0_IRQHandler                     
000043ab  TIMG12_IRQHandler                    
000043ab  TIMG6_IRQHandler                     
000043ab  TIMG7_IRQHandler                     
000043ab  TIMG8_IRQHandler                     
000042c5  TI_memcpy_small                      
00004345  TI_memset_small                      
00002299  Task_Add                             
00001367  Task_IdleFunction                    
00002e4d  Task_Init                            
00001e59  Task_Key                             
00001c8d  Task_Motor_PID                       
000027ed  Task_OLED                            
00000e8d  Task_Start                           
000043ab  UART0_IRQHandler                     
000043ab  UART1_IRQHandler                     
000043ab  UART2_IRQHandler                     
000043ab  UART3_IRQHandler                     
000039dd  _IQ24mpy                             
0000397d  _IQ24toF                             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000400  __SYSMEM_SIZE                        
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00004dc8  __TI_CINIT_Base                      
00004dd8  __TI_CINIT_Limit                     
00004dd8  __TI_CINIT_Warm                      
00004db4  __TI_Handler_Table_Base              
00004dc0  __TI_Handler_Table_Limit             
00003829  __TI_auto_init_nobinit_nopinit       
0000286d  __TI_decompress_lzss                 
000042d7  __TI_decompress_none                 
00002fc5  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
000041e5  __TI_zero_init_nomemset              
000011df  __adddf3                             
000020f7  __addsf3                             
00004bf0  __aeabi_ctype_table_                 
00004bf0  __aeabi_ctype_table_C                
000029e1  __aeabi_d2f                          
00003399  __aeabi_d2iz                         
000034fd  __aeabi_d2uiz                        
000011df  __aeabi_dadd                         
00002cc5  __aeabi_dcmpeq                       
00002d01  __aeabi_dcmpge                       
00002d15  __aeabi_dcmpgt                       
00002ced  __aeabi_dcmple                       
00002cd9  __aeabi_dcmplt                       
00001a7d  __aeabi_ddiv                         
00001d75  __aeabi_dmul                         
000011d5  __aeabi_dsub                         
202006dc  __aeabi_errno                        
00004395  __aeabi_errno_addr                   
00003601  __aeabi_f2d                          
000020f7  __aeabi_fadd                         
00002d29  __aeabi_fcmpeq                       
00002d65  __aeabi_fcmpge                       
00002d79  __aeabi_fcmpgt                       
00002d51  __aeabi_fcmple                       
00002d3d  __aeabi_fcmplt                       
000026dd  __aeabi_fmul                         
000020ed  __aeabi_fsub                         
00003a35  __aeabi_i2d                          
000037b1  __aeabi_i2f                          
00003075  __aeabi_idiv                         
0000249f  __aeabi_idiv0                        
00003075  __aeabi_idivmod                      
00002bfb  __aeabi_ldiv0                        
00003ce1  __aeabi_llsl                         
00003c19  __aeabi_lmul                         
0000439d  __aeabi_memcpy                       
0000439d  __aeabi_memcpy4                      
0000439d  __aeabi_memcpy8                      
00004329  __aeabi_memset                       
00004329  __aeabi_memset4                      
00004329  __aeabi_memset8                      
00003bf5  __aeabi_ui2d                         
000035c1  __aeabi_uidiv                        
000035c1  __aeabi_uidivmod                     
0000429d  __aeabi_uldivmod                     
00003ce1  __ashldi3                            
ffffffff  __binit__                            
00002b2d  __cmpdf2                             
00003865  __cmpsf2                             
00001a7d  __divdf3                             
00002b2d  __eqdf2                              
00003865  __eqsf2                              
00003601  __extendsfdf2                        
00003399  __fixdfsi                            
000034fd  __fixunsdfsi                         
00003a35  __floatsidf                          
000037b1  __floatsisf                          
00003bf5  __floatunsidf                        
00002961  __gedf2                              
000037ed  __gesf2                              
00002961  __gtdf2                              
000037ed  __gtsf2                              
00002b2d  __ledf2                              
00003865  __lesf2                              
00002b2d  __ltdf2                              
00003865  __ltsf2                              
UNDEFED   __mpu_init                           
00001d75  __muldf3                             
00003c19  __muldi3                             
000038a1  __muldsi3                            
000026dd  __mulsf3                             
00002b2d  __nedf2                              
00003865  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000011d5  __subdf3                             
000020ed  __subsf3                             
000029e1  __truncdfsf2                         
000023fd  __udivmoddi4                         
00003b81  _c_int00_noargs                      
20200000  _sys_memory                          
UNDEFED   _system_post_cinit                   
000043c9  _system_pre_init                     
000043a5  abort                                
000049c0  asc2_0806                            
000043d0  asc2_1608                            
00003641  atoi                                 
ffffffff  binit                                
202006e0  delayTick                            
00002f0d  frexp                                
00002f0d  frexpl                               
202004f0  gMotorAFrontBackup                   
20200590  gMotorBFrontBackup                   
00000000  interruptVectors                     
00002015  ldexp                                
00002015  ldexpl                               
00003cc1  main                                 
00003c5f  memccpy                              
000015e9  qsort                                
00002015  scalbn                               
00002015  scalbnl                              
202006e4  uwTick                               
00003a61  vsprintf                             
00004319  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000400  __SYSMEM_SIZE                        
00000e8d  Task_Start                           
0000103d  SYSCFG_DL_GPIO_init                  
000011d5  __aeabi_dsub                         
000011d5  __subdf3                             
000011df  __adddf3                             
000011df  __aeabi_dadd                         
00001367  Task_IdleFunction                    
00001369  PID_SProsc                           
000015e9  qsort                                
0000171d  OLED_ShowChar                        
0000196d  OLED_Init                            
00001a7d  __aeabi_ddiv                         
00001a7d  __divdf3                             
00001b89  DL_Timer_initFourCCPWMMode           
00001c8d  Task_Motor_PID                       
00001d75  __aeabi_dmul                         
00001d75  __muldf3                             
00001e59  Task_Key                             
00001f39  DL_SYSCTL_configSYSPLL               
00002015  ldexp                                
00002015  ldexpl                               
00002015  scalbn                               
00002015  scalbnl                              
000020ed  __aeabi_fsub                         
000020ed  __subsf3                             
000020f7  __addsf3                             
000020f7  __aeabi_fadd                         
000021c5  Motor_SetDuty                        
00002299  Task_Add                             
0000234d  GROUP1_IRQHandler                    
000023fd  __udivmoddi4                         
0000249f  __aeabi_idiv0                        
000024a1  I2C_OLED_WR_Byte                     
00002539  SYSCFG_DL_MotorAFront_init           
000025c5  SYSCFG_DL_MotorBFront_init           
00002651  SYSCFG_DL_initPower                  
000026dd  __aeabi_fmul                         
000026dd  __mulsf3                             
000027ed  Task_OLED                            
0000286d  __TI_decompress_lzss                 
000028e9  Motor_Start                          
00002961  __gedf2                              
00002961  __gtdf2                              
000029d5  Sys_GetTick                          
000029e1  __aeabi_d2f                          
000029e1  __truncdfsf2                         
00002a55  OLED_ShowString                      
00002ac3  I2C_OLED_Clear                       
00002b2d  __cmpdf2                             
00002b2d  __eqdf2                              
00002b2d  __ledf2                              
00002b2d  __ltdf2                              
00002b2d  __nedf2                              
00002bfb  __aeabi_ldiv0                        
00002bfd  DL_SYSCTL_setHFCLKSourceHFXTParams   
00002c61  Key_Read                             
00002cc5  __aeabi_dcmpeq                       
00002cd9  __aeabi_dcmplt                       
00002ced  __aeabi_dcmple                       
00002d01  __aeabi_dcmpge                       
00002d15  __aeabi_dcmpgt                       
00002d29  __aeabi_fcmpeq                       
00002d3d  __aeabi_fcmplt                       
00002d51  __aeabi_fcmple                       
00002d65  __aeabi_fcmpge                       
00002d79  __aeabi_fcmpgt                       
00002d8d  I2C_OLED_i2c_sda_unlock              
00002ded  SYSCFG_DL_I2C_OLED_init              
00002e4d  Task_Init                            
00002ead  DL_I2C_fillControllerTXFIFO          
00002f0d  frexp                                
00002f0d  frexpl                               
00002fc5  __TI_ltoa                            
00003075  __aeabi_idiv                         
00003075  __aeabi_idivmod                      
00003121  SYSCFG_DL_SYSCTL_init                
000032b5  OLED_Printf                          
00003301  SYSCFG_DL_ADC1_init                  
00003399  __aeabi_d2iz                         
00003399  __fixdfsi                            
000033e3  Motor_GetSpeed                       
00003475  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000034fd  __aeabi_d2uiz                        
000034fd  __fixunsdfsi                         
00003541  DL_ADC12_setClockConfig              
00003581  Interrupt_Init                       
000035c1  __aeabi_uidiv                        
000035c1  __aeabi_uidivmod                     
00003601  __aeabi_f2d                          
00003601  __extendsfdf2                        
00003641  atoi                                 
00003775  I2C_OLED_Set_Pos                     
000037b1  __aeabi_i2f                          
000037b1  __floatsisf                          
000037ed  __gesf2                              
000037ed  __gtsf2                              
00003829  __TI_auto_init_nobinit_nopinit       
00003865  __cmpsf2                             
00003865  __eqsf2                              
00003865  __lesf2                              
00003865  __ltsf2                              
00003865  __nesf2                              
000038a1  __muldsi3                            
000038dd  SYSCFG_DL_init                       
0000397d  _IQ24toF                             
000039dd  _IQ24mpy                             
00003a35  __aeabi_i2d                          
00003a35  __floatsidf                          
00003a61  vsprintf                             
00003a8d  PID_Init                             
00003b59  SysTick_Increasment                  
00003b81  _c_int00_noargs                      
00003bcf  DL_I2C_setClockConfig                
00003bf5  __aeabi_ui2d                         
00003bf5  __floatunsidf                        
00003c19  __aeabi_lmul                         
00003c19  __muldi3                             
00003c3d  PID_SetParams                        
00003c5f  memccpy                              
00003ca1  Delay                                
00003cc1  main                                 
00003ce1  __aeabi_llsl                         
00003ce1  __ashldi3                            
00003e6d  DL_Timer_setCaptCompUpdateMethod     
00003e89  DL_Timer_setClockConfig              
00004145  DL_Timer_setCaptureCompareOutCtl     
000041e5  __TI_zero_init_nomemset              
0000429d  __aeabi_uldivmod                     
000042c5  TI_memcpy_small                      
000042d7  __TI_decompress_none                 
000042f9  DL_Timer_setCaptureCompareValue      
00004309  SYSCFG_DL_SYSTICK_init               
00004319  wcslen                               
00004329  __aeabi_memset                       
00004329  __aeabi_memset4                      
00004329  __aeabi_memset8                      
00004345  TI_memset_small                      
00004353  DL_Common_delayCycles                
0000438d  SysTick_Handler                      
00004395  __aeabi_errno_addr                   
0000439d  __aeabi_memcpy                       
0000439d  __aeabi_memcpy4                      
0000439d  __aeabi_memcpy8                      
000043a5  abort                                
000043ab  ADC0_IRQHandler                      
000043ab  ADC1_IRQHandler                      
000043ab  AES_IRQHandler                       
000043ab  CANFD0_IRQHandler                    
000043ab  DAC0_IRQHandler                      
000043ab  DMA_IRQHandler                       
000043ab  Default_Handler                      
000043ab  GROUP0_IRQHandler                    
000043ab  HardFault_Handler                    
000043ab  I2C0_IRQHandler                      
000043ab  I2C1_IRQHandler                      
000043ab  NMI_Handler                          
000043ab  PendSV_Handler                       
000043ab  RTC_IRQHandler                       
000043ab  SPI0_IRQHandler                      
000043ab  SPI1_IRQHandler                      
000043ab  SVC_Handler                          
000043ab  TIMA0_IRQHandler                     
000043ab  TIMA1_IRQHandler                     
000043ab  TIMG0_IRQHandler                     
000043ab  TIMG12_IRQHandler                    
000043ab  TIMG6_IRQHandler                     
000043ab  TIMG7_IRQHandler                     
000043ab  TIMG8_IRQHandler                     
000043ab  UART0_IRQHandler                     
000043ab  UART1_IRQHandler                     
000043ab  UART2_IRQHandler                     
000043ab  UART3_IRQHandler                     
000043ae  C$$EXIT                              
000043af  HOSTexit                             
000043b3  Reset_Handler                        
000043c9  _system_pre_init                     
000043d0  asc2_1608                            
000049c0  asc2_0806                            
00004bf0  __aeabi_ctype_table_                 
00004bf0  __aeabi_ctype_table_C                
00004db4  __TI_Handler_Table_Base              
00004dc0  __TI_Handler_Table_Limit             
00004dc8  __TI_CINIT_Base                      
00004dd8  __TI_CINIT_Limit                     
00004dd8  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  _sys_memory                          
202004f0  gMotorAFrontBackup                   
20200590  gMotorBFrontBackup                   
20200630  ExISR_Flag                           
20200635  HD_count                             
20200638  Motor_Font_Left                      
20200680  Motor_Font_Right                     
202006c8  Motor                                
202006d0  Data_MotorEncoder                    
202006d4  Data_Motor_TarSpeed                  
202006d8  Data_Tracker_Offset                  
202006dc  __aeabi_errno                        
202006e0  delayTick                            
202006e4  uwTick                               
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[226 symbols]
