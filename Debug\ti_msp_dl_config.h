/*
 * Copyright (c) 2023, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, IN<PERSON>DENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 *  ============ ti_msp_dl_config.h =============
 *  Configured MSPM0 DriverLib module declarations
 *
 *  DO NOT EDIT - This file is generated for the MSPM0G350X
 *  by the SysConfig tool.
 */
#ifndef ti_msp_dl_config_h
#define ti_msp_dl_config_h

#define CONFIG_MSPM0G350X
#define CONFIG_MSPM0G3507

#if defined(__ti_version__) || defined(__TI_COMPILER_VERSION__)
#define SYSCONFIG_WEAK __attribute__((weak))
#elif defined(__IAR_SYSTEMS_ICC__)
#define SYSCONFIG_WEAK __weak
#elif defined(__GNUC__)
#define SYSCONFIG_WEAK __attribute__((weak))
#endif

#include <ti/devices/msp/msp.h>
#include <ti/driverlib/driverlib.h>
#include <ti/driverlib/m0p/dl_core.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
 *  ======== SYSCFG_DL_init ========
 *  Perform all required MSP DL initialization
 *
 *  This function should be called once at a point before any use of
 *  MSP DL.
 */


/* clang-format off */

#define POWER_STARTUP_DELAY                                                (16)


#define GPIO_HFXT_PORT                                                     GPIOA
#define GPIO_HFXIN_PIN                                             DL_GPIO_PIN_5
#define GPIO_HFXIN_IOMUX                                         (IOMUX_PINCM10)
#define GPIO_HFXOUT_PIN                                            DL_GPIO_PIN_6
#define GPIO_HFXOUT_IOMUX                                        (IOMUX_PINCM11)
#define CPUCLK_FREQ                                                     80000000



/* Defines for MotorAFront */
#define MotorAFront_INST                                                   TIMG7
#define MotorAFront_INST_IRQHandler                             TIMG7_IRQHandler
#define MotorAFront_INST_INT_IRQN                               (TIMG7_INT_IRQn)
#define MotorAFront_INST_CLK_FREQ                                        2000000
/* GPIO defines for channel 0 */
#define GPIO_MotorAFront_C0_PORT                                           GPIOB
#define GPIO_MotorAFront_C0_PIN                                   DL_GPIO_PIN_15
#define GPIO_MotorAFront_C0_IOMUX                                (IOMUX_PINCM32)
#define GPIO_MotorAFront_C0_IOMUX_FUNC               IOMUX_PINCM32_PF_TIMG7_CCP0
#define GPIO_MotorAFront_C0_IDX                              DL_TIMER_CC_0_INDEX
/* GPIO defines for channel 1 */
#define GPIO_MotorAFront_C1_PORT                                           GPIOB
#define GPIO_MotorAFront_C1_PIN                                   DL_GPIO_PIN_16
#define GPIO_MotorAFront_C1_IOMUX                                (IOMUX_PINCM33)
#define GPIO_MotorAFront_C1_IOMUX_FUNC               IOMUX_PINCM33_PF_TIMG7_CCP1
#define GPIO_MotorAFront_C1_IDX                              DL_TIMER_CC_1_INDEX

/* Defines for MotorBFront */
#define MotorBFront_INST                                                   TIMG6
#define MotorBFront_INST_IRQHandler                             TIMG6_IRQHandler
#define MotorBFront_INST_INT_IRQN                               (TIMG6_INT_IRQn)
#define MotorBFront_INST_CLK_FREQ                                        2000000
/* GPIO defines for channel 0 */
#define GPIO_MotorBFront_C0_PORT                                           GPIOB
#define GPIO_MotorBFront_C0_PIN                                    DL_GPIO_PIN_2
#define GPIO_MotorBFront_C0_IOMUX                                (IOMUX_PINCM15)
#define GPIO_MotorBFront_C0_IOMUX_FUNC               IOMUX_PINCM15_PF_TIMG6_CCP0
#define GPIO_MotorBFront_C0_IDX                              DL_TIMER_CC_0_INDEX
/* GPIO defines for channel 1 */
#define GPIO_MotorBFront_C1_PORT                                           GPIOB
#define GPIO_MotorBFront_C1_PIN                                    DL_GPIO_PIN_3
#define GPIO_MotorBFront_C1_IOMUX                                (IOMUX_PINCM16)
#define GPIO_MotorBFront_C1_IOMUX_FUNC               IOMUX_PINCM16_PF_TIMG6_CCP1
#define GPIO_MotorBFront_C1_IDX                              DL_TIMER_CC_1_INDEX




/* Defines for I2C_OLED */
#define I2C_OLED_INST                                                       I2C0
#define I2C_OLED_INST_IRQHandler                                 I2C0_IRQHandler
#define I2C_OLED_INST_INT_IRQN                                     I2C0_INT_IRQn
#define I2C_OLED_BUS_SPEED_HZ                                             400000
#define GPIO_I2C_OLED_SDA_PORT                                             GPIOA
#define GPIO_I2C_OLED_SDA_PIN                                      DL_GPIO_PIN_0
#define GPIO_I2C_OLED_IOMUX_SDA                                   (IOMUX_PINCM1)
#define GPIO_I2C_OLED_IOMUX_SDA_FUNC                    IOMUX_PINCM1_PF_I2C0_SDA
#define GPIO_I2C_OLED_SCL_PORT                                             GPIOA
#define GPIO_I2C_OLED_SCL_PIN                                      DL_GPIO_PIN_1
#define GPIO_I2C_OLED_IOMUX_SCL                                   (IOMUX_PINCM2)
#define GPIO_I2C_OLED_IOMUX_SCL_FUNC                    IOMUX_PINCM2_PF_I2C0_SCL



/* Port definition for Pin Group GPIO_MPU6050 */
#define GPIO_MPU6050_PORT                                                (GPIOA)

/* Defines for PIN_INT: GPIOA.30 with pinCMx 5 on package pin 37 */
// pins affected by this interrupt request:["PIN_INT"]
#define GPIO_MPU6050_INT_IRQN                                   (GPIOA_INT_IRQn)
#define GPIO_MPU6050_INT_IIDX                   (DL_INTERRUPT_GROUP1_IIDX_GPIOA)
#define GPIO_MPU6050_PIN_INT_IIDX                           (DL_GPIO_IIDX_DIO30)
#define GPIO_MPU6050_PIN_INT_PIN                                (DL_GPIO_PIN_30)
#define GPIO_MPU6050_PIN_INT_IOMUX                                (IOMUX_PINCM5)
/* Defines for LED1: GPIOB.1 with pinCMx 13 on package pin 48 */
#define LED_LED1_PORT                                                    (GPIOB)
#define LED_LED1_PIN                                             (DL_GPIO_PIN_1)
#define LED_LED1_IOMUX                                           (IOMUX_PINCM13)
/* Defines for LED2: GPIOA.23 with pinCMx 53 on package pin 24 */
#define LED_LED2_PORT                                                    (GPIOA)
#define LED_LED2_PIN                                            (DL_GPIO_PIN_23)
#define LED_LED2_IOMUX                                           (IOMUX_PINCM53)
/* Defines for LED3: GPIOB.14 with pinCMx 31 on package pin 2 */
#define LED_LED3_PORT                                                    (GPIOB)
#define LED_LED3_PIN                                            (DL_GPIO_PIN_14)
#define LED_LED3_IOMUX                                           (IOMUX_PINCM31)
/* Defines for LED4: GPIOA.7 with pinCMx 14 on package pin 49 */
#define LED_LED4_PORT                                                    (GPIOA)
#define LED_LED4_PIN                                             (DL_GPIO_PIN_7)
#define LED_LED4_IOMUX                                           (IOMUX_PINCM14)
/* Defines for KEY4: GPIOA.26 with pinCMx 59 on package pin 30 */
#define KEY_KEY4_PORT                                                    (GPIOA)
#define KEY_KEY4_PIN                                            (DL_GPIO_PIN_26)
#define KEY_KEY4_IOMUX                                           (IOMUX_PINCM59)
/* Defines for KEY2: GPIOB.24 with pinCMx 52 on package pin 23 */
#define KEY_KEY2_PORT                                                    (GPIOB)
#define KEY_KEY2_PIN                                            (DL_GPIO_PIN_24)
#define KEY_KEY2_IOMUX                                           (IOMUX_PINCM52)
/* Defines for KEY3: GPIOA.27 with pinCMx 60 on package pin 31 */
#define KEY_KEY3_PORT                                                    (GPIOA)
#define KEY_KEY3_PIN                                            (DL_GPIO_PIN_27)
#define KEY_KEY3_IOMUX                                           (IOMUX_PINCM60)
/* Port definition for Pin Group SPD_READER_A */
#define SPD_READER_A_PORT                                                (GPIOB)

/* Defines for FONT_LEFT_A: GPIOB.11 with pinCMx 28 on package pin 63 */
// pins affected by this interrupt request:["FONT_LEFT_A","FONT_RIGHT_A"]
#define SPD_READER_A_INT_IRQN                                   (GPIOB_INT_IRQn)
#define SPD_READER_A_INT_IIDX                   (DL_INTERRUPT_GROUP1_IIDX_GPIOB)
#define SPD_READER_A_FONT_LEFT_A_IIDX                       (DL_GPIO_IIDX_DIO11)
#define SPD_READER_A_FONT_LEFT_A_PIN                            (DL_GPIO_PIN_11)
#define SPD_READER_A_FONT_LEFT_A_IOMUX                           (IOMUX_PINCM28)
/* Defines for FONT_RIGHT_A: GPIOB.9 with pinCMx 26 on package pin 61 */
#define SPD_READER_A_FONT_RIGHT_A_IIDX                       (DL_GPIO_IIDX_DIO9)
#define SPD_READER_A_FONT_RIGHT_A_PIN                            (DL_GPIO_PIN_9)
#define SPD_READER_A_FONT_RIGHT_A_IOMUX                          (IOMUX_PINCM26)
/* Port definition for Pin Group SPD_READER_B */
#define SPD_READER_B_PORT                                                (GPIOB)

/* Defines for FONT_LEFT_B: GPIOB.10 with pinCMx 27 on package pin 62 */
#define SPD_READER_B_FONT_LEFT_B_PIN                            (DL_GPIO_PIN_10)
#define SPD_READER_B_FONT_LEFT_B_IOMUX                           (IOMUX_PINCM27)
/* Defines for FONT_RIGHT_B: GPIOB.8 with pinCMx 25 on package pin 60 */
#define SPD_READER_B_FONT_RIGHT_B_PIN                            (DL_GPIO_PIN_8)
#define SPD_READER_B_FONT_RIGHT_B_IOMUX                          (IOMUX_PINCM25)
/* Defines for DAT: GPIOA.24 with pinCMx 54 on package pin 25 */
#define Serial_DAT_PORT                                                  (GPIOA)
#define Serial_DAT_PIN                                          (DL_GPIO_PIN_24)
#define Serial_DAT_IOMUX                                         (IOMUX_PINCM54)
/* Defines for CLK: GPIOB.20 with pinCMx 48 on package pin 19 */
#define Serial_CLK_PORT                                                  (GPIOB)
#define Serial_CLK_PIN                                          (DL_GPIO_PIN_20)
#define Serial_CLK_IOMUX                                         (IOMUX_PINCM48)





/* clang-format on */

void SYSCFG_DL_init(void);
void SYSCFG_DL_initPower(void);
void SYSCFG_DL_GPIO_init(void);
void SYSCFG_DL_SYSCTL_init(void);
void SYSCFG_DL_MotorAFront_init(void);
void SYSCFG_DL_MotorBFront_init(void);
void SYSCFG_DL_I2C_OLED_init(void);

void SYSCFG_DL_SYSTICK_init(void);


bool SYSCFG_DL_saveConfiguration(void);
bool SYSCFG_DL_restoreConfiguration(void);

#ifdef __cplusplus
}
#endif

#endif /* ti_msp_dl_config_h */
