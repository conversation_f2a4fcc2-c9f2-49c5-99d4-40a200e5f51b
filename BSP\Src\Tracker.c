#include "Tracker.h"

uint8_t ret;
uint8_t HD_count;

#define DIS_INRERVAL _IQ(1.5) //传感器间距

unsigned short Analog[8]={0};
unsigned short white[8]={1800,1800,1800,1800,1800,1800,1800,1800};
unsigned short black[8]={300,300,300,300,300,300,300,300};
unsigned short Normal[8];
No_MCU_Sensor sensor;
unsigned char Digtal;

void HD_Init(void){
    No_MCU_Ganv_Sensor_Init_Frist(&sensor);
    No_Mcu_Ganv_Sensor_Task_Without_tick(&sensor);
    Get_Anolog_Value(&sensor,Analog);

    while(HD_count != 3){
        Get_Anolog_Value(&sensor,Analog);
        OLED_Printf(0, 16*0, 8, "Analog %d-%d-%d-%d-%d-%d-%d-%d\r\n",Analog[0],Analog[1],<PERSON>log[2],<PERSON>log[3],<PERSON>log[4],<PERSON>log[5],Analog[6],<PERSON>log[7]);
        // memset(rx_buff,0,256);
        // sprintf((char *)rx_buff,"Analog %d-%d-%d-%d-%d-%d-%d-%d\r\n",Analog[0],Analog[1],Analog[2],Analog[3],Analog[4],Analog[5],Analog[6],Analog[7]);
        // printf((char*)rx_buff);
        if(HD_count == 1){
            Get_Anolog_Value(&sensor,Analog);
            memcpy(white,Analog,16);
            OLED_Printf(0, 16*0, 8, "Analog %d-%d-%d-%d-%d-%d-%d-%d\r\n",Analog[0],Analog[1],Analog[2],Analog[3],Analog[4],Analog[5],Analog[6],Analog[7]);
            // memset(rx_buff,0,sizeof(rx_buff));
            // sprintf((char *)rx_buff,"white %d-%d-%d-%d-%d-%d-%d-%d\r\n",white[0],white[1],white[2],white[3],white[4],white[5],white[6],white[7]);
            // printf((char*)rx_buff);
        }
         else if(HD_count == 2){
                Get_Anolog_Value(&sensor,Analog);
                memcpy(black,Analog,16);
                OLED_Printf(0, 16*0, 8, "Analog %d-%d-%d-%d-%d-%d-%d-%d\r\n",Analog[0],Analog[1],Analog[2],Analog[3],Analog[4],Analog[5],Analog[6],Analog[7]);
                // memset(rx_buff,0,sizeof(rx_buff));
                // sprintf((char *)rx_buff,"black %d-%d-%d-%d-%d-%d-%d-%d\r\n",black[0],black[1],black[2],black[3],black[4],black[5],black[6],black[7]);
                // printf((char*)rx_buff);
        }
        // delay_ms(1000);
    }

    No_MCU_Ganv_Sensor_Init(&sensor,white,black);
    delay_ms(100);  
}

/**
 * @brief 读取循迹传感器并计算位置偏差
 * 
 * @param tck_ptr 8路传感器数据数组指针
 * @param offset_ptr 位置偏差值指针 (单位:cm, 负值表示偏左，正值表示偏右)
 * @return true 成功读取并计算偏差 
 * @return false 读取失败或参数错误
 */
bool Tracker_Read(uint8_t *tck_ptr, _iq *offset_ptr)
{
	uint8_t i;
    uint8_t test;
    
    if (tck_ptr == NULL || offset_ptr == NULL) return false;

    ret = 0;


    for (i = 0; i < 8; ++i) {
        tck_ptr[i]=(ret>>i)&0x01;
    }


    _iq pos_sum = _IQ(0);
    uint8_t cnt = 0;

    // 加权平均法计算位置
    for (uint8_t i = 0; i < 8; i++)
    {
        if (tck_ptr[i] == TRACK_ON)
        {
            // 传感器位置权重: 0,1,2,3,4,5,6,7 对应 -5.25,-3.75,-2.25,-0.75,0.75,2.25,3.75,5.25 cm
            _iq sensor_pos = _IQmpy(_IQ(i - 3.5f), DIS_INRERVAL);
            pos_sum += sensor_pos;
            cnt++;
        }
    }

    if (cnt == 0)
    {
        // 没有检测到线，保持上次偏差值
        return false;
    }

    // 计算加权平均位置偏差
    *offset_ptr = _IQdiv(pos_sum, _IQ(cnt));

    return true;

}


// void Task_GraySensor(void *para)
// {
//     // 调用传感器处理函数（无时基版本，与gpio_toggle_output.c逻辑一致）
//     No_Mcu_Ganv_Sensor_Task_Without_tick(&GraySensor);

//     // 获取数字值（二值化结果）
//     Gray_Digtal = Get_Digtal_For_User(&GraySensor);

//     // 获取模拟值（原始ADC值）
//     Get_Anolog_Value(&GraySensor, Gray_Anolog);

//     // 获取归一化值（0~4095，基于12位ADC）
//     Get_Normalize_For_User(&GraySensor, Gray_Normal);
// }

